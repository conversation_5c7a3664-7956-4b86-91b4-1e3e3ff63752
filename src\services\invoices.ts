
import { supabase } from "@/integrations/supabase/client";
import type { Invoice, InvoiceItem, Payment } from "@/lib/data";
import { RepairStatus } from "@/lib/data";
import { v4 as uuidv4 } from 'uuid';
import { generateShortId, getRepairJobById, updateRepairJob } from './repairs';

// Generate a business-friendly invoice number
function generateInvoiceNumber(): string {
  const year = new Date().getFullYear();
  const randomNum = Math.floor(1000 + Math.random() * 9000);
  return `INV-${year}-${randomNum}`;
}

// Get all invoices with optional filtering
export async function getInvoices(filters?: {
  customerId?: string;
  status?: 'paid' | 'partial' | 'pending' | 'overdue';
  dateFrom?: Date;
  dateTo?: Date;
}): Promise<Invoice[]> {
  // First, get all repair jobs to have their IDs available
  const { data: repairJobs, error: repairJobsError } = await supabase
    .from('repair_jobs')
    .select('id');

  if (repairJobsError) {
    console.error("Error fetching repair jobs for ID mapping:", repairJobsError);
  }

  // Create a map of repair job IDs for quick lookup
  const repairJobIds = new Set();
  if (repairJobs) {
    repairJobs.forEach(job => repairJobIds.add(job.id));
  }

  let query = supabase
    .from('invoices')
    .select(`
      *,
      customers(name, display_id)
    `);

  // Apply filters if provided
  if (filters) {
    if (filters.customerId) {
      query = query.eq('customer_id', filters.customerId);
    }
    if (filters.status) {
      query = query.eq('payment_status', filters.status);
    }
    if (filters.dateFrom) {
      query = query.gte('created_at', filters.dateFrom.toISOString());
    }
    if (filters.dateTo) {
      query = query.lte('created_at', filters.dateTo.toISOString());
    }
  }

  const { data, error } = await query;

  if (error) {
    console.error("Error fetching invoices:", error);
    throw error;
  }

  const invoicesWithItems = await Promise.all(data.map(async (invoice) => {
    const { data: itemsData, error: itemsError } = await supabase
      .from('invoice_items')
      .select('*')
      .eq('invoice_id', invoice.id);

    if (itemsError) {
      console.error("Error fetching invoice items:", itemsError);
    }

    const items = itemsError ? [] : itemsData.map(item => ({
      id: item.id,
      description: item.description,
      quantity: item.quantity,
      unitPrice: Number(item.unit_price),
      total: Number(item.total),
      itemType: item.item_type || 'part',
      partId: item.part_id,
      discountAmount: item.discount_amount ? Number(item.discount_amount) : undefined,
      discountType: item.discount_type as 'percentage' | 'fixed' | undefined,
      notes: item.notes
    }));

    // Get payments for this invoice
    const { data: paymentsData, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .eq('invoice_id', invoice.id);

    const payments = paymentsError ? [] : paymentsData.map(payment => ({
      id: payment.id,
      invoiceId: payment.invoice_id,
      amount: Number(payment.amount),
      paymentMethod: payment.payment_method as 'cash' | 'card' | 'upi' | 'bank',
      paymentDate: new Date(payment.payment_date),
      notes: payment.notes,
      receiptNumber: payment.receipt_number
    }));

    // Convert database items to our model
    const enhancedItems = items.map(item => ({
      ...item,
      itemType: item.itemType || 'part' // Default to 'part' for backward compatibility
    }));

    // Try to get the display ID for the repair job
    let displayRepairId = invoice.repair_job_id;

    // If the repair_job_id is a UUID and exists in our repair jobs, generate a short ID
    if (invoice.repair_job_id && invoice.repair_job_id.includes('-')) {
      if (repairJobIds.has(invoice.repair_job_id)) {
        try {
          displayRepairId = generateShortId(invoice.repair_job_id);
        } catch (error) {
          console.warn("Could not generate short ID for repair job", error);
        }
      }
    }

    return {
      id: invoice.id,
      invoiceNumber: invoice.invoice_number || `INV-${invoice.id.substring(0, 8)}`, // Fallback for old data
      repairJobId: displayRepairId,
      customerId: invoice.customers.display_id || `C${invoice.customer_id.substring(0, 4)}`,
      customerName: invoice.customers.name,
      items: enhancedItems,
      subtotal: Number(invoice.subtotal),
      discountAmount: invoice.discount_amount ? Number(invoice.discount_amount) : undefined,
      discountType: invoice.discount_type as 'percentage' | 'fixed' | undefined,
      total: Number(invoice.total),
      paidAmount: Number(invoice.paid_amount),
      balance: Number(invoice.balance),
      paymentStatus: invoice.payment_status as 'paid' | 'partial' | 'pending' | 'overdue',
      payments: payments,
      notes: invoice.notes || '',
      terms: invoice.terms || 'Payment due upon receipt',
      createdAt: new Date(invoice.created_at),
      dueDate: invoice.due_date ? new Date(invoice.due_date) : undefined,
      createdBy: invoice.created_by
    };
  }));

  return invoicesWithItems;
}

export async function createInvoice(invoice: Omit<Invoice, "id" | "createdAt" | "invoiceNumber">): Promise<Invoice> {
  // Generate a business-friendly invoice number
  const invoiceNumber = generateInvoiceNumber();

  // Check if an invoice already exists for this repair job
  const { data: existingInvoices, error: checkError } = await supabase
    .from('invoices')
    .select('id, invoice_number')
    .eq('repair_job_id', invoice.repairJobId);

  if (checkError) {
    console.warn("Error checking for existing invoices:", checkError);
  } else if (existingInvoices && existingInvoices.length > 0) {
    console.warn(`Creating another invoice for repair job ${invoice.repairJobId}. Existing invoice(s): ${existingInvoices.length}`);
  }

  // Start a transaction
  const { data: invoiceData, error: invoiceError } = await supabase
    .from('invoices')
    .insert({
      invoice_number: invoiceNumber,
      repair_job_id: invoice.repairJobId,
      customer_id: invoice.customerId,
      labor_cost: invoice.items.filter(item => item.itemType === 'labor').reduce((sum, item) => sum + item.total, 0),
      subtotal: invoice.subtotal,
      discount_amount: invoice.discountAmount,
      discount_type: invoice.discountType,
      tax_rate: 0, // Set to 0 since GST is removed
      tax: 0, // Set to 0 since GST is removed
      total: invoice.total,
      paid_amount: invoice.paidAmount,
      balance: invoice.balance,
      payment_status: invoice.paymentStatus,
      payment_method: invoice.paymentMethod,
      notes: invoice.notes,
      terms: invoice.terms,
      due_date: invoice.dueDate?.toISOString(),
      created_by: invoice.createdBy
    })
    .select(`
      *,
      customers(name, display_id)
    `)
    .single();

  if (invoiceError) {
    console.error("Error creating invoice:", invoiceError);
    console.error("Invoice data that failed:", {
      invoice_number: invoiceNumber,
      repair_job_id: invoice.repairJobId,
      customer_id: invoice.customerId,
      // other fields...
    });
    throw invoiceError;
  }

  // Insert invoice items
  if (invoice.items && invoice.items.length > 0) {
    const invoiceItems = invoice.items.map(item => ({
      invoice_id: invoiceData.id,
      description: item.description,
      quantity: item.quantity,
      unit_price: item.unitPrice,
      total: item.total,
      item_type: item.itemType || 'part',
      part_id: item.partId,
      discount_amount: item.discountAmount,
      discount_type: item.discountType,
      notes: item.notes
    }));

    const { error: itemsError } = await supabase
      .from('invoice_items')
      .insert(invoiceItems);

    if (itemsError) {
      console.error("Error creating invoice items:", itemsError);
      // Consider rolling back the invoice here in a real application
    }
  }

  // Create initial payment if paid amount > 0
  let payments: Payment[] = [];
  if (invoice.paidAmount > 0) {
    const paymentId = uuidv4();
    const receiptNumber = `REC-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`;

    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .insert({
        id: paymentId,
        invoice_id: invoiceData.id,
        amount: invoice.paidAmount,
        payment_method: invoice.paymentMethod || 'cash',
        payment_date: new Date().toISOString(),
        receipt_number: receiptNumber,
        notes: 'Initial payment'
      })
      .select()
      .single();

    if (!paymentError && paymentData) {
      payments.push({
        id: paymentData.id,
        invoiceId: paymentData.invoice_id,
        amount: Number(paymentData.amount),
        paymentMethod: paymentData.payment_method as 'cash' | 'card' | 'upi' | 'bank',
        paymentDate: new Date(paymentData.payment_date),
        notes: paymentData.notes,
        receiptNumber: paymentData.receipt_number
      });

      // If payment status is 'paid' (full payment), update the repair job status to 'delivered'
      if (invoiceData.payment_status === 'paid' && invoiceData.repair_job_id) {
        try {
          // Get the repair job using our service
          const repairJob = await getRepairJobById(invoiceData.repair_job_id);

          if (repairJob && repairJob.status === 'completed') {
            // Update the repair job status to 'delivered' using our service
            await updateRepairJob({
              ...repairJob,
              status: 'delivered' as RepairStatus,
              deliveredAt: new Date()
            });

            console.log(`Repair job ${invoiceData.repair_job_id} status updated to 'delivered' during invoice creation`);
          }
        } catch (error) {
          console.error("Error updating repair job status during invoice creation:", error);
          // Don't throw error here, as the invoice was created successfully
        }
      }
    }
  }

  // Try to get the display ID for the repair job
  let displayRepairId = invoiceData.repair_job_id;

  // If the repair_job_id is a UUID, try to generate a short ID
  if (invoiceData.repair_job_id && invoiceData.repair_job_id.includes('-')) {
    try {
      // First check if this is a valid repair job ID
      const { data: repairJob, error: repairJobError } = await supabase
        .from('repair_jobs')
        .select('id')
        .eq('id', invoiceData.repair_job_id)
        .single();

      if (!repairJobError && repairJob) {
        displayRepairId = generateShortId(invoiceData.repair_job_id);
      }
    } catch (error) {
      console.warn("Could not generate short ID for repair job", error);
    }
  }

  return {
    id: invoiceData.id,
    invoiceNumber: invoiceData.invoice_number,
    repairJobId: displayRepairId,
    customerId: invoiceData.customers.display_id || `C${invoiceData.customer_id.substring(0, 4)}`,
    customerName: invoiceData.customers.name,
    items: invoice.items || [],
    subtotal: Number(invoiceData.subtotal),
    discountAmount: invoiceData.discount_amount ? Number(invoiceData.discount_amount) : undefined,
    discountType: invoiceData.discount_type as 'percentage' | 'fixed' | undefined,
    total: Number(invoiceData.total),
    paidAmount: Number(invoiceData.paid_amount),
    balance: Number(invoiceData.balance),
    paymentStatus: invoiceData.payment_status as 'paid' | 'partial' | 'pending' | 'overdue',
    payments: payments,
    notes: invoiceData.notes,
    terms: invoiceData.terms,
    createdAt: new Date(invoiceData.created_at),
    dueDate: invoiceData.due_date ? new Date(invoiceData.due_date) : undefined,
    createdBy: invoiceData.created_by
  };
}

// Add a new payment to an invoice
export async function addPayment(invoiceId: string, payment: Omit<Payment, 'id' | 'invoiceId'>): Promise<Payment> {
  const paymentId = uuidv4();

  // Insert the payment
  const { data: paymentData, error: paymentError } = await supabase
    .from('payments')
    .insert({
      id: paymentId,
      invoice_id: invoiceId,
      amount: payment.amount,
      payment_method: payment.paymentMethod,
      payment_date: payment.paymentDate.toISOString(),
      receipt_number: payment.receiptNumber,
      notes: payment.notes
    })
    .select()
    .single();

  if (paymentError) {
    console.error("Error adding payment:", paymentError);
    throw paymentError;
  }

  // Get the invoice to update the paid amount and balance
  const { data: invoiceData, error: invoiceError } = await supabase
    .from('invoices')
    .select('*')
    .eq('id', invoiceId)
    .single();

  if (invoiceError) {
    console.error("Error fetching invoice for payment update:", invoiceError);
    throw invoiceError;
  }

  // Calculate new paid amount and balance
  const newPaidAmount = Number(invoiceData.paid_amount) + payment.amount;

  // Check if the new paid amount exceeds the total
  if (newPaidAmount > Number(invoiceData.total)) {
    throw new Error(`Payment amount exceeds the remaining balance. Maximum payment allowed: ${Number(invoiceData.total) - Number(invoiceData.paid_amount)}`);
  }

  const newBalance = Number(invoiceData.total) - newPaidAmount;

  // Determine new payment status
  let newPaymentStatus = 'pending';
  if (newBalance <= 0) {
    newPaymentStatus = 'paid';
  } else if (newPaidAmount > 0) {
    newPaymentStatus = 'partial';
  }

  // Check if invoice is overdue
  if (newPaymentStatus !== 'paid' && invoiceData.due_date) {
    const dueDate = new Date(invoiceData.due_date);
    if (dueDate < new Date()) {
      newPaymentStatus = 'overdue';
    }
  }

  // Update the invoice
  const { error: updateError } = await supabase
    .from('invoices')
    .update({
      paid_amount: newPaidAmount,
      balance: newBalance,
      payment_status: newPaymentStatus
    })
    .eq('id', invoiceId);

  if (updateError) {
    console.error("Error updating invoice after payment:", updateError);
    throw updateError;
  }

  // If payment status is now 'paid', update the repair job status to 'delivered'
  if (newPaymentStatus === 'paid' && invoiceData.repair_job_id) {
    try {
      // Get the repair job using our service
      const repairJob = await getRepairJobById(invoiceData.repair_job_id);

      if (repairJob && repairJob.status === 'completed') {
        // Update the repair job status to 'delivered' using our service
        await updateRepairJob({
          ...repairJob,
          status: 'delivered' as RepairStatus,
          deliveredAt: new Date()
        });

        console.log(`Repair job ${invoiceData.repair_job_id} status updated to 'delivered'`);
      }
    } catch (error) {
      console.error("Error updating repair job status:", error);
      // Don't throw error here, as the payment was successful
    }
  }

  return {
    id: paymentData.id,
    invoiceId: paymentData.invoice_id,
    amount: Number(paymentData.amount),
    paymentMethod: paymentData.payment_method,
    paymentDate: new Date(paymentData.payment_date),
    notes: paymentData.notes,
    receiptNumber: paymentData.receipt_number
  };
}

export async function updateInvoice(invoice: Invoice): Promise<Invoice> {
  const { data, error } = await supabase
    .from('invoices')
    .update({
      invoice_number: invoice.invoiceNumber,
      repair_job_id: invoice.repairJobId,
      customer_id: invoice.customerId,
      labor_cost: invoice.items.filter(item => item.itemType === 'labor').reduce((sum, item) => sum + item.total, 0),
      subtotal: invoice.subtotal,
      discount_amount: invoice.discountAmount,
      discount_type: invoice.discountType,
      tax_rate: 0, // Set to 0 since GST is removed
      tax: 0, // Set to 0 since GST is removed
      total: invoice.total,
      paid_amount: invoice.paidAmount,
      balance: invoice.balance,
      payment_status: invoice.paymentStatus,
      payment_method: invoice.paymentMethod,
      notes: invoice.notes,
      terms: invoice.terms,
      due_date: invoice.dueDate?.toISOString(),
      created_by: invoice.createdBy
    })
    .eq('id', invoice.id)
    .select(`
      *,
      customers(name, display_id)
    `)
    .single();

  if (error) {
    console.error("Error updating invoice:", error);
    throw error;
  }

  // Try to get the display ID for the repair job
  let displayRepairId = data.repair_job_id;

  // If the repair_job_id is a UUID, try to generate a short ID
  if (data.repair_job_id && data.repair_job_id.includes('-')) {
    try {
      // First check if this is a valid repair job ID
      const { data: repairJob, error: repairJobError } = await supabase
        .from('repair_jobs')
        .select('id')
        .eq('id', data.repair_job_id)
        .single();

      if (!repairJobError && repairJob) {
        displayRepairId = generateShortId(data.repair_job_id);
      }
    } catch (error) {
      console.warn("Could not generate short ID for repair job", error);
    }
  }

  // If payment status is 'paid' (full payment), update the repair job status to 'delivered'
  if (data.payment_status === 'paid' && data.repair_job_id) {
    try {
      // First check if the repair job exists and is in 'completed' status
      const { data: repairJob, error: repairJobError } = await supabase
        .from('repair_jobs')
        .select('id, status')
        .eq('id', data.repair_job_id)
        .single();

      if (!repairJobError && repairJob && repairJob.status === 'completed') {
        // Update the repair job status to 'delivered'
        const { error: repairUpdateError } = await supabase
          .from('repair_jobs')
          .update({
            status: 'delivered',
            delivered_at: new Date().toISOString()
          })
          .eq('id', data.repair_job_id);

        if (repairUpdateError) {
          console.error("Error updating repair job status during invoice update:", repairUpdateError);
          // Don't throw error here, as the invoice was updated successfully
        } else {
          console.log(`Repair job ${data.repair_job_id} status updated to 'delivered' during invoice update`);
        }
      }
    } catch (error) {
      console.error("Error checking repair job status during invoice update:", error);
      // Don't throw error here, as the invoice was updated successfully
    }
  }

  return {
    id: data.id,
    invoiceNumber: data.invoice_number || `INV-${data.id.substring(0, 8)}`,
    repairJobId: displayRepairId,
    customerId: data.customer_id,
    customerName: data.customers.name,
    items: invoice.items, // Keep the existing items
    subtotal: Number(data.subtotal),
    discountAmount: data.discount_amount ? Number(data.discount_amount) : undefined,
    discountType: data.discount_type as 'percentage' | 'fixed' | undefined,
    total: Number(data.total),
    paidAmount: Number(data.paid_amount),
    balance: Number(data.balance),
    paymentStatus: data.payment_status as 'paid' | 'partial' | 'pending' | 'overdue',
    payments: invoice.payments, // Keep the existing payments
    notes: data.notes,
    terms: data.terms,
    createdAt: new Date(data.created_at),
    dueDate: data.due_date ? new Date(data.due_date) : undefined,
    createdBy: data.created_by
  };
}

// Get invoice by ID with all related data
export async function getInvoiceById(id: string): Promise<Invoice | null> {
  console.log("getInvoiceById called with id:", id);
  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      customers(name)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error("Error fetching invoice:", error);
    return null;
  }

  // Get invoice items
  const { data: itemsData, error: itemsError } = await supabase
    .from('invoice_items')
    .select('*')
    .eq('invoice_id', id);

  if (itemsError) {
    console.error("Error fetching invoice items:", itemsError);
    return null;
  }

  const items = itemsData.map(item => ({
    id: item.id,
    description: item.description,
    quantity: item.quantity,
    unitPrice: Number(item.unit_price),
    total: Number(item.total),
    itemType: item.item_type || 'part',
    partId: item.part_id,
    discountAmount: item.discount_amount ? Number(item.discount_amount) : undefined,
    discountType: item.discount_type as 'percentage' | 'fixed' | undefined,
    notes: item.notes
  }));

  // Get payments
  const { data: paymentsData, error: paymentsError } = await supabase
    .from('payments')
    .select('*')
    .eq('invoice_id', id);

  const payments = paymentsError ? [] : paymentsData.map(payment => ({
    id: payment.id,
    invoiceId: payment.invoice_id,
    amount: Number(payment.amount),
    paymentMethod: payment.payment_method as 'cash' | 'card' | 'upi' | 'bank',
    paymentDate: new Date(payment.payment_date),
    notes: payment.notes,
    receiptNumber: payment.receipt_number
  }));

  // Try to get the display ID for the repair job
  let displayRepairId = data.repair_job_id;
  console.log(`[DEBUG] getInvoiceById - Original repair_job_id: ${data.repair_job_id}`);

  // If the repair_job_id is a UUID, try to generate a short ID
  if (data.repair_job_id && data.repair_job_id.includes('-')) {
    try {
      // First check if this is a valid repair job ID
      const { data: repairJob, error: repairJobError } = await supabase
        .from('repair_jobs')
        .select('id')
        .eq('id', data.repair_job_id)
        .single();

      if (repairJobError) {
        console.log(`[DEBUG] Repair job not found in database: ${data.repair_job_id}`);
      } else {
        console.log(`[DEBUG] Valid repair job found: ${repairJob.id}`);
        displayRepairId = generateShortId(data.repair_job_id);
        console.log(`[DEBUG] Generated short ID: ${displayRepairId}`);
      }
    } catch (error) {
      console.warn("[DEBUG] Could not generate short ID for repair job", error);
    }
  }

  return {
    id: data.id,
    invoiceNumber: data.invoice_number || `INV-${data.id.substring(0, 8)}`,
    repairJobId: displayRepairId,
    customerId: data.customer_id,
    customerName: data.customers.name,
    items: items,
    subtotal: Number(data.subtotal),
    discountAmount: data.discount_amount ? Number(data.discount_amount) : undefined,
    discountType: data.discount_type as 'percentage' | 'fixed' | undefined,
    total: Number(data.total),
    paidAmount: Number(data.paid_amount),
    balance: Number(data.balance),
    paymentStatus: data.payment_status as 'paid' | 'partial' | 'pending' | 'overdue',
    payments: payments,
    notes: data.notes || '',
    terms: data.terms || 'Payment due upon receipt',
    createdAt: new Date(data.created_at),
    dueDate: data.due_date ? new Date(data.due_date) : undefined,
    createdBy: data.created_by
  };
}

/**
 * Delete an invoice and all related data (items and payments)
 * @param id Invoice ID to delete
 * @returns true if successful, throws error otherwise
 */
export async function deleteInvoice(id: string): Promise<boolean> {
  // Start by deleting related data

  // 1. Delete invoice items
  const { error: itemsError } = await supabase
    .from('invoice_items')
    .delete()
    .eq('invoice_id', id);

  if (itemsError) {
    console.error("Error deleting invoice items:", itemsError);
    throw itemsError;
  }

  // 2. Delete payments
  const { error: paymentsError } = await supabase
    .from('payments')
    .delete()
    .eq('invoice_id', id);

  if (paymentsError) {
    console.error("Error deleting invoice payments:", paymentsError);
    throw paymentsError;
  }

  // 3. Finally delete the invoice itself
  const { error: invoiceError } = await supabase
    .from('invoices')
    .delete()
    .eq('id', id);

  if (invoiceError) {
    console.error("Error deleting invoice:", invoiceError);
    throw invoiceError;
  }

  return true;
}