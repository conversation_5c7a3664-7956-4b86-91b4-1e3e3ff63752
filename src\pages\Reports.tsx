import React, { useState, useEffect } from "react";
import Header from "@/components/Layout/Header";
import Sidebar from "@/components/Layout/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SimpleDatePicker } from "@/components/ui/simple-date-picker";
import { Download, Printer } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { getRevenueReport, getRepairAnalytics, getInventoryReport, getCustomerReport } from "@/services/reports";
import { ReportFilters } from "@/services/reports";
import { formatINR } from "@/lib/currency";
import { objectsToCSV, downloadCSV } from "@/lib/csv";
import FinancialReports from "@/components/Reports/FinancialReports";
import RepairReports from "@/components/Reports/RepairReports";
import InventoryReports from "@/components/Reports/InventoryReports";
import CustomerReports from "@/components/Reports/CustomerReports";
import { RepairStatus } from "@/lib/data";

const Reports = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [activeTab, setActiveTab] = useState("financial");
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<ReportFilters>({
    dateFrom: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // First day of current month
    dateTo: new Date()
  });

  // Report data states
  const [financialData, setFinancialData] = useState(null);
  const [repairData, setRepairData] = useState(null);
  const [inventoryData, setInventoryData] = useState(null);
  const [customerData, setCustomerData] = useState(null);

  // Load data based on active tab and filters
  useEffect(() => {
    const loadReportData = async () => {
      setIsLoading(true);

      try {
        switch (activeTab) {
          case "financial":
            if (!financialData) {
              const data = await getRevenueReport(filters);
              setFinancialData(data);
            }
            break;
          case "repairs":
            if (!repairData) {
              const data = await getRepairAnalytics(filters);
              setRepairData(data);
            }
            break;
          case "inventory":
            if (!inventoryData) {
              const data = await getInventoryReport();
              setInventoryData(data);
            }
            break;
          case "customers":
            if (!customerData) {
              const data = await getCustomerReport();
              setCustomerData(data);
            }
            break;
        }
      } catch (error) {
        console.error(`Error loading ${activeTab} report data:`, error);
      } finally {
        setIsLoading(false);
      }
    };

    loadReportData();
  }, [activeTab, filters]);

  // Handle filter changes
  const handleDateFromChange = (date: Date) => {
    // Validate that from date is not after to date
    if (filters.dateTo && date > filters.dateTo) {
      toast.error("From date cannot be after To date", {
        description: "Please select a valid date range"
      });
      return;
    }

    setFilters({
      ...filters,
      dateFrom: date
    });

    // Reset data to force reload with new filters
    setFinancialData(null);
    setRepairData(null);

    toast.success("Date filter updated", {
      description: `Reports will show data from ${date.toLocaleDateString('en-IN')}`
    });
  };

  const handleDateToChange = (date: Date) => {
    // Validate that to date is not before from date
    if (filters.dateFrom && date < filters.dateFrom) {
      toast.error("To date cannot be before From date", {
        description: "Please select a valid date range"
      });
      return;
    }

    setFilters({
      ...filters,
      dateTo: date
    });

    // Reset data to force reload with new filters
    setFinancialData(null);
    setRepairData(null);

    toast.success("Date filter updated", {
      description: `Reports will show data until ${date.toLocaleDateString('en-IN')}`
    });
  };

  const handleStatusFilterChange = (statuses: string) => {
    // If "all" is selected, set repairStatus to undefined to show all statuses
    if (statuses === 'all') {
      setFilters({
        ...filters,
        repairStatus: undefined
      });

      toast.success("Status filter updated", {
        description: "Showing repairs with all statuses"
      });
    } else {
      const statusArray = statuses.split(',') as RepairStatus[];

      setFilters({
        ...filters,
        repairStatus: statusArray.length > 0 ? statusArray : undefined
      });

      const statusLabels = statusArray.map(status => {
        switch(status) {
          case "inProgress": return "In Progress";
          case "on_hold": return "On Hold";
          default: return status.charAt(0).toUpperCase() + status.slice(1);
        }
      }).join(", ");

      toast.success("Status filter updated", {
        description: `Showing repairs with status: ${statusLabels}`
      });
    }

    // Reset repair data to force reload with new filters
    setRepairData(null);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Export report as PDF
  const handleExportPDF = () => {
    // This would be implemented with a PDF generation library
    alert("PDF export functionality will be implemented with a PDF generation library");
  };

  // Export report as CSV
  const handleExportCSV = () => {
    let csvData: any[] = [];
    let headers: { key: string; label: string }[] = [];
    let filename = "";

    switch (activeTab) {
      case "financial":
        if (!financialData) return;

        // Prepare daily revenue data
        csvData = financialData.dailyRevenue.map(item => ({
          Date: new Date(item.date),
          "Total Revenue": item.total,
          "Collected": item.paid,
          "Pending": item.pending
        }));

        headers = [
          { key: "Date", label: "Date" },
          { key: "Total Revenue", label: "Total Revenue (₹)" },
          { key: "Collected", label: "Collected (₹)" },
          { key: "Pending", label: "Pending (₹)" }
        ];

        filename = `financial-report-${new Date().toISOString().split('T')[0]}.csv`;
        break;

      case "repairs":
        if (!repairData) return;

        // Prepare repair status data
        const statusData = repairData.statusDistribution.map(item => ({
          Status: item.status === "inProgress" ? "In Progress" :
                 item.status === "on_hold" ? "On Hold" :
                 item.status.charAt(0).toUpperCase() + item.status.slice(1),
          Count: item.count,
          Percentage: ((item.count / repairData.statusDistribution.reduce((sum, s) => sum + s.count, 0)) * 100).toFixed(1) + "%"
        }));

        // Prepare repair time data
        const timeData = repairData.repairTimes
          .filter(repair => repair.repairTime !== null)
          .map(repair => ({
            ID: repair.id,
            "Device Type": repair.deviceType,
            Brand: repair.brand,
            Model: repair.model,
            Status: repair.status === "inProgress" ? "In Progress" :
                   repair.status === "on_hold" ? "On Hold" :
                   repair.status.charAt(0).toUpperCase() + repair.status.slice(1),
            "Repair Time (days)": repair.repairTime
          }));

        // Choose which data to export
        csvData = timeData.length > 0 ? timeData : statusData;

        headers = timeData.length > 0 ? [
          { key: "ID", label: "Repair ID" },
          { key: "Device Type", label: "Device Type" },
          { key: "Brand", label: "Brand" },
          { key: "Model", label: "Model" },
          { key: "Status", label: "Status" },
          { key: "Repair Time (days)", label: "Repair Time (days)" }
        ] : [
          { key: "Status", label: "Status" },
          { key: "Count", label: "Count" },
          { key: "Percentage", label: "Percentage" }
        ];

        filename = `repair-analytics-${new Date().toISOString().split('T')[0]}.csv`;
        break;

      case "inventory":
        if (!inventoryData) return;

        // Prepare inventory data
        csvData = inventoryData.inventoryItems.map(item => ({
          Name: item.name,
          Category: item.category,
          Quantity: item.quantity,
          Threshold: item.threshold,
          Price: item.price,
          Cost: item.cost,
          Value: item.value,
          "Total Used": item.totalUsed,
          Status: item.stockStatus === "low" ? "Low Stock" : "Normal"
        }));

        headers = [
          { key: "Name", label: "Item Name" },
          { key: "Category", label: "Category" },
          { key: "Quantity", label: "Quantity" },
          { key: "Threshold", label: "Threshold" },
          { key: "Price", label: "Price (₹)" },
          { key: "Cost", label: "Cost (₹)" },
          { key: "Value", label: "Total Value (₹)" },
          { key: "Total Used", label: "Total Used" },
          { key: "Status", label: "Stock Status" }
        ];

        filename = `inventory-report-${new Date().toISOString().split('T')[0]}.csv`;
        break;

      case "customers":
        if (!customerData) return;

        // Prepare customer data
        csvData = customerData.customers.map(customer => ({
          Name: customer.name,
          Email: customer.email || "-",
          Phone: customer.phone,
          "Repair Count": customer.repairCount,
          "Total Spent": customer.totalSpent,
          "Average Repair Value": customer.averageRepairValue,
          "Last Repair": customer.lastRepair ? new Date(customer.lastRepair) : "-"
        }));

        headers = [
          { key: "Name", label: "Customer Name" },
          { key: "Email", label: "Email" },
          { key: "Phone", label: "Phone" },
          { key: "Repair Count", label: "Repair Count" },
          { key: "Total Spent", label: "Total Spent (₹)" },
          { key: "Average Repair Value", label: "Avg. Repair Value (₹)" },
          { key: "Last Repair", label: "Last Repair Date" }
        ];

        filename = `customer-report-${new Date().toISOString().split('T')[0]}.csv`;
        break;
    }

    if (csvData.length === 0) {
      toast.error("No data to export", {
        description: "Please wait for the data to load or try a different filter"
      });
      return;
    }

    // Convert data to CSV and download
    const csvContent = objectsToCSV(csvData, headers);
    downloadCSV(csvContent, filename);

    toast.success("CSV Export Successful", {
      description: `${filename} has been downloaded`
    });
  };

  return (
    <div className="flex min-h-screen">
      <Sidebar isOpen={sidebarOpen} toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? "md:ml-64" : "md:ml-16"}`}>
        <Header toggleSidebar={() => setSidebarOpen(!sidebarOpen)} title="Reports & Analytics" />

        <main className="p-4 md:p-6">
          {/* Filters and Export */}
          <div className="mb-6 flex flex-col md:flex-row gap-4 justify-between">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Date From Filter */}
              <SimpleDatePicker
                label="From Date"
                value={filters.dateFrom}
                onChange={handleDateFromChange}
              />

              {/* Date To Filter */}
              <SimpleDatePicker
                label="To Date"
                value={filters.dateTo}
                onChange={handleDateToChange}
              />

              {/* Status Filter - Only show for repairs tab */}
              {activeTab === "repairs" && (
                <Select onValueChange={handleStatusFilterChange}>
                  <SelectTrigger className="w-full sm:w-[200px]">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="inProgress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                    <SelectItem value="pending,inProgress">Active Repairs</SelectItem>
                    <SelectItem value="completed,delivered">Finished Repairs</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={handleExportPDF}>
                <Printer className="mr-2 h-4 w-4" />
                Print
              </Button>
              <Button variant="outline" onClick={handleExportCSV}>
                <Download className="mr-2 h-4 w-4" />
                Export CSV
              </Button>
            </div>
          </div>

          {/* Report Tabs */}
          <Tabs defaultValue="financial" value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="mb-6 grid grid-cols-2 md:grid-cols-4 gap-2">
              <TabsTrigger value="financial">Financial</TabsTrigger>
              <TabsTrigger value="repairs">Repairs</TabsTrigger>
              <TabsTrigger value="inventory">Inventory</TabsTrigger>
              <TabsTrigger value="customers">Customers</TabsTrigger>
            </TabsList>

            <TabsContent value="financial">
              <FinancialReports data={financialData} isLoading={isLoading} />
            </TabsContent>

            <TabsContent value="repairs">
              <RepairReports data={repairData} isLoading={isLoading} />
            </TabsContent>

            <TabsContent value="inventory">
              <InventoryReports data={inventoryData} isLoading={isLoading} />
            </TabsContent>

            <TabsContent value="customers">
              <CustomerReports data={customerData} isLoading={isLoading} />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Reports;
