import { jsPDF } from 'jspdf';
import { Invoice } from './data';
// @ts-ignore
import autoTable from 'jspdf-autotable';
import { getSettings } from '@/services/settings';

// PDF-specific currency formatter that works better with jsPDF
const formatCurrencyForPDF = (amount: number): string => {
  return `Rs. ${amount.toLocaleString('en-IN', {
    maximumFractionDigits: 0
  })}`;
};

export const generateInvoicePDF = async (invoice: Invoice): Promise<void> => {
  // Get company settings
  const settings = await getSettings();
  // Create a new PDF document
  const doc = new jsPDF();

  // Set up document properties
  const pageWidth = doc.internal.pageSize.getWidth();
  const margin = 15;
  let y = 20;

  // Add company header
  doc.setFontSize(16);
  doc.text(settings.company.companyName, 105, y, { align: 'center' });
  y += 7;

  doc.setFontSize(10);
  doc.text(settings.company.address, 105, y, { align: 'center' });
  y += 5;
  doc.text(`Phone: ${settings.company.phone} | Email: ${settings.company.email}`, 105, y, { align: 'center' });
  y += 10;

  // Add invoice header
  doc.setFontSize(14);
  doc.text(`Invoice #${invoice.invoiceNumber || invoice.id}`, 105, y, { align: 'center' });
  y += 7;

  // Add invoice details
  doc.setFontSize(10);
  doc.text(`Date: ${invoice.createdAt.toLocaleDateString('en-IN')}`, margin, y);
  doc.text(`Status: ${invoice.paymentStatus.charAt(0).toUpperCase() + invoice.paymentStatus.slice(1)}`, pageWidth - margin, y, { align: 'right' });
  y += 5;

  if (invoice.dueDate) {
    doc.text(`Due Date: ${invoice.dueDate.toLocaleDateString('en-IN')}`, margin, y);
    y += 5;
  }

  // Add customer details
  doc.setFontSize(12);
  doc.text('Customer Information', margin, y);
  y += 7;

  doc.setFontSize(10);
  doc.text(`Name: ${invoice.customerName}`, margin, y);
  y += 5;
  doc.text(`Customer ID: ${invoice.customerId}`, margin, y);
  y += 5;
  doc.text(`Repair Job ID: ${invoice.repairJobId}`, margin, y);
  y += 10;

  // Add invoice items table
  doc.setFontSize(12);
  doc.text('Invoice Items', margin, y);
  y += 7;

  // Create invoice items table
  const itemsTableData = {
    head: [['Description', 'Quantity', 'Unit Price', 'Total']],
    body: invoice.items.map(item => [
      item.description,
      item.quantity.toString(),
      formatCurrencyForPDF(item.unitPrice),
      formatCurrencyForPDF(item.total)
    ])
  };

  // Add the table to the document
  const itemsTable = autoTable(doc, {
    startY: y,
    head: itemsTableData.head,
    body: itemsTableData.body,
    theme: 'grid',
    headStyles: {
      fillColor: [240, 240, 240],
      textColor: [0, 0, 0],
      fontStyle: 'bold'
    },
    columnStyles: {
      0: { cellWidth: 'auto' },
      1: { cellWidth: 20, halign: 'right' },
      2: { cellWidth: 30, halign: 'right' },
      3: { cellWidth: 30, halign: 'right' }
    },
    styles: {
      fontSize: 9,
      cellPadding: 3
    },
    margin: { left: margin, right: margin }
  });

  // Get the final Y position after the table
  // @ts-ignore - autoTable returns an object with finalY property
  y = (itemsTable && itemsTable.lastAutoTable) ? itemsTable.lastAutoTable.finalY + 10 : y + 30;

  // Add totals
  const totalsX = pageWidth - margin - 80; // Width for labels
  const totalsWidth = pageWidth - margin - totalsX;
  const lineSpacing = 10; // Increased line spacing for better readability

  // Draw a box around the totals section
  const totalsStartY = y;

  // Create a clean financial summary box
  doc.setFillColor(250, 250, 250);
  doc.rect(totalsX - 15, totalsStartY - 8, totalsWidth + 30, 80, 'F');

  // Subtotal
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.text('Subtotal:', totalsX, y);
  doc.text(formatCurrencyForPDF(invoice.subtotal), pageWidth - margin, y, { align: 'right' });
  y += lineSpacing;

  // Add discount if applicable
  if (invoice.discountAmount && invoice.discountType) {
    const discountValue = invoice.discountType === 'percentage' ?
      (invoice.subtotal * (invoice.discountAmount / 100)) :
      invoice.discountAmount;

    const discountLabel = invoice.discountType === 'percentage' ?
      `Discount (${invoice.discountAmount}%):` :
      'Discount (Fixed):';

    doc.text(discountLabel, totalsX, y);
    doc.text(`-${formatCurrencyForPDF(discountValue)}`, pageWidth - margin, y, { align: 'right' });
    y += lineSpacing;
  }

  // Add a bit of extra space before total
  y += 3;

  // Total - Make it bold and larger
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('Total:', totalsX, y);
  doc.text(formatCurrencyForPDF(invoice.total), pageWidth - margin, y, { align: 'right' });
  y += lineSpacing + 3; // Extra spacing after total

  // Payment details
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');
  doc.text('Paid Amount:', totalsX, y);
  doc.text(formatCurrencyForPDF(invoice.paidAmount), pageWidth - margin, y, { align: 'right' });
  y += lineSpacing;

  // Balance
  doc.text('Balance:', totalsX, y);
  doc.text(formatCurrencyForPDF(invoice.balance), pageWidth - margin, y, { align: 'right' });

  y += 25; // Extra spacing after the totals section

  // Add payment history if available
  if (invoice.payments && invoice.payments.length > 0) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Payment History', margin, y);
    y += 7;

    // Create payment history table
    const paymentsTable = autoTable(doc, {
      startY: y,
      head: [['Date', 'Method', 'Amount', 'Receipt #']],
      body: invoice.payments.map(payment => [
        payment.paymentDate.toLocaleDateString('en-IN'),
        payment.paymentMethod.toUpperCase(),
        formatCurrencyForPDF(payment.amount),
        payment.receiptNumber || 'N/A'
      ]),
      theme: 'grid',
      headStyles: {
        fillColor: [240, 240, 240],
        textColor: [0, 0, 0],
        fontStyle: 'bold'
      },
      columnStyles: {
        0: { cellWidth: 30 },
        1: { cellWidth: 30 },
        2: { cellWidth: 30, halign: 'right' },
        3: { cellWidth: 'auto' }
      },
      styles: {
        fontSize: 9,
        cellPadding: 3
      },
      margin: { left: margin, right: margin }
    });

    // Get the final Y position after the table
    // @ts-ignore - autoTable returns an object with finalY property
    y = (paymentsTable && paymentsTable.lastAutoTable) ? paymentsTable.lastAutoTable.finalY + 10 : y + 30;
  }

  // Add notes and terms if available
  if (invoice.notes || invoice.terms) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Additional Information', margin, y);
    y += 7;

    if (invoice.notes) {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Notes:', margin, y);
      y += 5;

      doc.setFont('helvetica', 'normal');
      doc.text(invoice.notes, margin, y);
      y += 10;
    }

    if (invoice.terms) {
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Terms & Conditions:', margin, y);
      y += 5;

      doc.setFont('helvetica', 'normal');
      doc.text(invoice.terms, margin, y);
      y += 10;
    }
  }

  // Add footer
  const footerY = doc.internal.pageSize.getHeight() - 20;
  doc.setFontSize(9);
  doc.setFont('helvetica', 'normal');
  doc.text('Thank you for your business!', 105, footerY - 10, { align: 'center' });
  doc.text('This is a computer-generated invoice and does not require a signature.', 105, footerY - 5, { align: 'center' });
  doc.text(`For any queries, please contact us at ${settings.company.email}`, 105, footerY, { align: 'center' });

  // Save the PDF
  doc.save(`Invoice-${invoice.invoiceNumber || invoice.id}.pdf`);
};
