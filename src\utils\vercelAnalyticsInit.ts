/**
 * This utility manually initializes Vercel Analytics if it's not already available
 */

// Define the Vercel Analytics window interface
declare global {
  interface Window {
    va?: (command: string, ...args: any[]) => void;
    vercelAnalytics?: {
      track: (event: string, properties?: any) => void;
    };
  }
}

/**
 * Manually initialize Vercel Analytics
 * This is a fallback in case the Analytics component doesn't properly initialize
 */
export function initVercelAnalytics(): void {
  if (typeof window === 'undefined') return;

  // Check if Vercel Analytics is already initialized
  if (window.va) {
    console.info('[VercelAnalytics] Already initialized with window.va');
    return;
  }

  try {
    console.info('[VercelAnalytics] Manually initializing...');

    // Create the va function if it doesn't exist
    if (!window.va) {
      window.va = function va(command: string, ...args: any[]) {
        (window.va.q = window.va.q || []).push([command, ...args]);
      };
      window.va.q = window.va.q || [];

      // Send an initialization event
      window.va('event', {
        name: 'va_initialized',
        timestamp: new Date().toISOString()
      });
    }

    // Load the Vercel Analytics script
    const script = document.createElement('script');
    script.src = '/_vercel/insights/script.js';
    script.defer = true;
    script.id = 'vercel-analytics';
    script.setAttribute('data-auto-track', 'true');

    // Add event listeners to track script loading
    script.onload = () => {
      console.info('[VercelAnalytics] Script loaded successfully');

      // Send a script loaded event
      if (window.va) {
        window.va('event', {
          name: 'va_script_loaded',
          timestamp: new Date().toISOString()
        });
      }
    };

    script.onerror = (error) => {
      console.error('[VercelAnalytics] Script failed to load:', error);
    };

    document.head.appendChild(script);

    console.info('[VercelAnalytics] Manual initialization complete');
  } catch (error) {
    console.error('[VercelAnalytics] Failed to manually initialize:', error);
  }
}

/**
 * Check if Vercel Analytics is available
 * @returns Object with availability status
 */
export function checkVercelAnalyticsAvailability(): {
  vaAvailable: boolean;
  vercelAnalyticsAvailable: boolean;
  isProduction: boolean;
} {
  if (typeof window === 'undefined') {
    return {
      vaAvailable: false,
      vercelAnalyticsAvailable: false,
      isProduction: false
    };
  }

  const isProduction = window.location.hostname !== 'localhost' &&
                      !window.location.hostname.includes('127.0.0.1');

  return {
    vaAvailable: typeof window.va !== 'undefined',
    vercelAnalyticsAvailable: typeof window.vercelAnalytics !== 'undefined',
    isProduction
  };
}
