import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip, Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid } from "recharts";

interface RepairReportsProps {
  data: any;
  isLoading: boolean;
}

const RepairReports: React.FC<RepairReportsProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">No repair data available</p>
      </div>
    );
  }

  const { statusDistribution, deviceTypeDistribution, brandDistribution, repairTimes, averageRepairTime } = data;

  // Colors for charts
  const STATUS_COLORS = {
    pending: "#FFA500",
    inProgress: "#3B82F6",
    completed: "#10B981",
    delivered: "#8B5CF6",
    cancelled: "#EF4444",
    on_hold: "#9CA3AF"
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  // Format status labels
  const formatStatusLabel = (status) => {
    switch(status) {
      case "inProgress": return "In Progress";
      case "on_hold": return "On Hold";
      default: return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Calculate repair time distribution
  const repairTimeDistribution = repairTimes
    .filter(repair => repair.repairTime !== null)
    .reduce((acc, repair) => {
      const timeRange = repair.repairTime <= 1 ? "0-1 day" :
                        repair.repairTime <= 3 ? "1-3 days" :
                        repair.repairTime <= 7 ? "3-7 days" : "7+ days";
      
      if (!acc[timeRange]) {
        acc[timeRange] = 0;
      }
      
      acc[timeRange]++;
      return acc;
    }, {});

  const repairTimeData = Object.entries(repairTimeDistribution).map(([range, count]) => ({
    range,
    count
  }));

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Repairs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statusDistribution.reduce((sum, item) => sum + item.count, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              All repair jobs
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Average Repair Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {averageRepairTime.toFixed(1)} days
            </div>
            <p className="text-xs text-muted-foreground">
              From creation to completion
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(() => {
                const completed = statusDistribution.find(s => s.status === "completed" || s.status === "delivered")?.count || 0;
                const total = statusDistribution.reduce((sum, item) => sum + item.count, 0);
                return total > 0 ? `${((completed / total) * 100).toFixed(1)}%` : "0%";
              })()}
            </div>
            <p className="text-xs text-muted-foreground">
              Completed or delivered repairs
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="status" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="status">Status Distribution</TabsTrigger>
          <TabsTrigger value="device">Device Types</TabsTrigger>
          <TabsTrigger value="brand">Brands</TabsTrigger>
          <TabsTrigger value="time">Repair Time</TabsTrigger>
        </TabsList>
        
        <TabsContent value="status" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Repair Status Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={statusDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={150}
                      fill="#8884d8"
                      dataKey="count"
                      nameKey="status"
                      label={({ status, percent }) => `${formatStatusLabel(status)}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {statusDistribution.map((entry, index) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={STATUS_COLORS[entry.status] || COLORS[index % COLORS.length]} 
                        />
                      ))}
                    </Pie>
                    <Tooltip 
                      formatter={(value) => [value, "Repairs"]}
                      labelFormatter={(label) => formatStatusLabel(label)}
                    />
                    <Legend 
                      formatter={(value) => formatStatusLabel(value)}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="device" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Device Type Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={deviceTypeDistribution}
                    margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis 
                      dataKey="deviceType" 
                      type="category"
                      width={150}
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip formatter={(value) => [value, "Repairs"]} />
                    <Legend />
                    <Bar dataKey="count" name="Number of Repairs" fill="#0088FE" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="brand" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Brand Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={brandDistribution}
                    margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis 
                      dataKey="brand" 
                      type="category"
                      width={150}
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip formatter={(value) => [value, "Repairs"]} />
                    <Legend />
                    <Bar dataKey="count" name="Number of Repairs" fill="#00C49F" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="time" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Repair Time Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={repairTimeData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="range" />
                    <YAxis />
                    <Tooltip formatter={(value) => [value, "Repairs"]} />
                    <Legend />
                    <Bar dataKey="count" name="Number of Repairs" fill="#FFBB28" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Repair Status Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="py-3 px-4 text-left">Status</th>
                  <th className="py-3 px-4 text-right">Count</th>
                  <th className="py-3 px-4 text-right">Percentage</th>
                </tr>
              </thead>
              <tbody>
                {statusDistribution.map((status, index) => {
                  const total = statusDistribution.reduce((sum, item) => sum + item.count, 0);
                  const percentage = total > 0 ? (status.count / total) * 100 : 0;
                  
                  return (
                    <tr key={index} className="border-b hover:bg-muted/50">
                      <td className="py-3 px-4">{formatStatusLabel(status.status)}</td>
                      <td className="py-3 px-4 text-right">{status.count}</td>
                      <td className="py-3 px-4 text-right">{percentage.toFixed(1)}%</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RepairReports;
