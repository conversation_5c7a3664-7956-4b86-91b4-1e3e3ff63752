
import React, { useState, useEffect } from "react";
import Header from "@/components/Layout/Header";
import Sidebar from "@/components/Layout/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Loader2, Database, CheckCircle, AlertCircle, Save, RotateCcw } from "lucide-react";
import { testDatabaseConnection } from "@/utils/dbTest";
import { useSettings } from "@/contexts/SettingsContext";
import { Settings as SettingsType } from "@/services/settings";


const Settings = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isTestingDb, setIsTestingDb] = useState(false);
  const [dbTestResult, setDbTestResult] = useState<boolean | null>(null);
  const { settings, isLoading, saveSettings, resetSettings } = useSettings();

  // Local state for form values
  const [formValues, setFormValues] = useState<SettingsType>({} as SettingsType);
  const [isSaving, setIsSaving] = useState(false);

  // Update form values when settings change
  useEffect(() => {
    if (settings && Object.keys(settings).length > 0) {
      setFormValues(settings);
    }
  }, [settings]);

  // Handle form input changes
  const handleInputChange = (section: 'company' | 'application', field: string, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      await saveSettings(formValues);
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle reset settings
  const handleResetSettings = async () => {
    try {
      setIsSaving(true);
      await resetSettings();
      // The form values will be updated automatically via the useEffect when settings change
    } catch (error) {
      console.error("Error resetting settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex min-h-screen">
      <Sidebar isOpen={sidebarOpen} toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? "md:ml-64" : "md:ml-16"}`}>
        <Header toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isOpen={sidebarOpen} />

        <main className="px-4 py-6 md:px-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Settings</h1>
            {isLoading && <Loader2 className="h-5 w-5 animate-spin" />}
          </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="companyName">Company Name</Label>
                      <Input
                        id="companyName"
                        value={formValues?.company?.companyName || ''}
                        onChange={(e) => handleInputChange('company', 'companyName', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={formValues?.company?.phone || ''}
                        onChange={(e) => handleInputChange('company', 'phone', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formValues?.company?.email || ''}
                        onChange={(e) => handleInputChange('company', 'email', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        value={formValues?.company?.address || ''}
                        onChange={(e) => handleInputChange('company', 'address', e.target.value)}
                      />
                    </div>

                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Application Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Database Connection</p>
                      <p className="text-sm text-gray-500">Test connection to Supabase database</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {dbTestResult === true && <CheckCircle className="h-5 w-5 text-green-500" />}
                      {dbTestResult === false && <AlertCircle className="h-5 w-5 text-red-500" />}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={async () => {
                          setIsTestingDb(true);
                          setDbTestResult(null);
                          try {
                            const result = await testDatabaseConnection();
                            setDbTestResult(result);
                          } catch (error) {
                            setDbTestResult(false);
                          } finally {
                            setIsTestingDb(false);
                          }
                        }}
                        disabled={isTestingDb}
                      >
                        {isTestingDb ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Testing...
                          </>
                        ) : (
                          <>
                            <Database className="mr-2 h-4 w-4" />
                            Test Connection
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Currency</p>
                      <p className="text-sm text-gray-500">Default currency for all transactions</p>
                    </div>
                    <div className="flex items-center">
                      <p className="font-medium mr-2">{formValues?.application?.currency || 'INR'}</p>
                    </div>
                  </div>



                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Dark Mode</p>
                      <p className="text-sm text-gray-500">Toggle application theme</p>
                    </div>
                    <Switch
                      checked={formValues?.application?.darkMode || false}
                      onCheckedChange={(checked) => handleInputChange('application', 'darkMode', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">SMS Notifications</p>
                      <p className="text-sm text-gray-500">Send SMS updates to customers</p>
                    </div>
                    <Switch
                      checked={formValues?.application?.smsNotifications || false}
                      onCheckedChange={(checked) => handleInputChange('application', 'smsNotifications', checked)}
                    />
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end">
                <Button
                  className="mr-2"
                  variant="outline"
                  onClick={handleResetSettings}
                  disabled={isLoading || isSaving}
                >
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Reset
                </Button>
                <Button
                  onClick={handleSaveSettings}
                  disabled={isLoading || isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </div>
          </main>
        </div>
      </div>
  );
};

export default Settings;
