import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Printer } from "lucide-react";
import { RepairJob } from "@/lib/data";
import { useSettings } from "@/contexts/SettingsContext";
import { toast } from "@/components/ui/sonner";

interface RepairTicketPrintProps {
  repair: RepairJob;
  buttonText?: string;
  buttonVariant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  buttonSize?: "default" | "sm" | "lg" | "icon";
  className?: string;
  onPrintSuccess?: () => void;
  onPrintError?: (error: Error) => void;
}

const RepairTicketPrint: React.FC<RepairTicketPrintProps> = ({
  repair,
  buttonText = "Print Ticket",
  buttonVariant = "outline",
  buttonSize = "sm",
  className = "",
  onPrintSuccess,
  onPrintError
}) => {
  const { settings } = useSettings();
  // Format date to a more readable format
  const formattedDate = repair.createdAt.toLocaleDateString("en-IN", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });

  // Create a URL for the QR code that points to the repair details
  const qrCodeValue = `repair:${repair.id}`;

  // Function to handle print button click
  const handlePrint = () => {
    try {
      // Create a new window
      const printWindow = window.open('', '_blank');

      if (!printWindow) {
        const error = new Error('Failed to open print window. Pop-up might be blocked.');
        console.error(error.message);
        toast.error("Failed to open print window", {
          description: "Pop-up might be blocked. Please allow pop-ups for this site."
        });
        if (onPrintError) onPrintError(error);
        return;
      }

      // Write the ticket HTML to the new window
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Repair Ticket - ${repair.id}</title>
            <style>
              body {
                font-family: monospace;
                margin: 0;
                padding: 0;
                width: 76mm;
              }
              .ticket {
                padding: 8px;
                font-size: 10px;
                line-height: 1.2;
              }
              .header {
                text-align: center;
                margin-bottom: 8px;
              }
              .shop-name {
                font-size: 12px;
                font-weight: bold;
              }
              .id-date {
                display: flex;
                justify-content: space-between;
                border-top: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                padding: 4px 0;
                margin-bottom: 8px;
              }
              .section {
                margin-bottom: 8px;
              }
              .label {
                font-weight: bold;
              }
              .qr-code {
                text-align: center;
                margin: 8px 0;
              }
              .footer {
                text-align: center;
                font-size: 8px;
                border-top: 1px solid #ddd;
                padding-top: 4px;
                margin-top: 8px;
              }
              @media print {
                @page {
                  size: 80mm auto;
                  margin: 0;
                }
                body {
                  width: 76mm;
                }
              }
            </style>
          </head>
          <body>
            <div class="ticket">
              <div class="header">
                <div class="shop-name">${settings?.company?.companyName || 'Mobile Repair Shop'}</div>
                <div>${settings?.company?.phone || '+91 98765 43210'}</div>
              </div>

              <div class="id-date">
                <div class="label">ID: ${repair.id}</div>
                <div>${formattedDate}</div>
              </div>

              <div class="section">
                <div class="label">Customer:</div>
                <div>${repair.customerName}</div>
              </div>

              <div class="section">
                <div class="label">Device:</div>
                <div>${repair.deviceType} - ${repair.brand} ${repair.model}</div>
                ${repair.serialNumber ? `<div>S/N: ${repair.serialNumber}</div>` : ''}
              </div>

              <div class="section">
                <div class="label">Issue:</div>
                <div>${repair.issueDescription.substring(0, 100)}${repair.issueDescription.length > 100 ? "..." : ""}</div>
              </div>

              <div class="section">
                <div class="label">Status:</div>
                <div style="text-transform: uppercase;">${repair.status}</div>
              </div>

              <div class="qr-code">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=${encodeURIComponent(qrCodeValue)}" width="100" height="100" alt="QR Code" />
              </div>

              <div class="footer">
                <div>Thank you for your business!</div>
                <div>Scan QR code for repair status</div>
                <div>${settings?.company?.email || '<EMAIL>'}</div>
              </div>
            </div>
            <script>
              // Auto print when loaded
              window.onload = function() {
                window.print();
                // Close the window after printing (or after cancel)
                window.onfocus = function() {
                  setTimeout(function() {
                    window.close();
                    // Call success callback after printing
                    if (window.opener && typeof window.opener.printSuccess === 'function') {
                      window.opener.printSuccess();
                    }
                  }, 500);
                };
              };
            </script>
          </body>
        </html>
      `);

      // Define success callback in the opener window
      (window as any).printSuccess = () => {
        console.log("Print completed successfully");
        if (onPrintSuccess) onPrintSuccess();
      };

      // Finish writing and trigger the print
      printWindow.document.close();
    } catch (error) {
      console.error("Print failed:", error);
      toast.error("Print failed", {
        description: "There was an error while trying to print the repair ticket."
      });
      if (onPrintError) onPrintError(error instanceof Error ? error : new Error(String(error)));
    }
  };

  return (
    <Button
      variant={buttonVariant}
      size={buttonSize}
      onClick={handlePrint}
      className={className}
    >
      <Printer className="mr-2 h-4 w-4" />
      {buttonText}
    </Button>
  );
};

export default RepairTicketPrint;
