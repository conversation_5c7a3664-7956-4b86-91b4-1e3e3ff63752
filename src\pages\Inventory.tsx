
import React, { useState, useEffect } from "react";
import Header from "@/components/Layout/Header";
import Sidebar from "@/components/Layout/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Search, PlusCircle, Edit, RefreshCcw, Trash2, AlertTriangle, Loader2 } from "lucide-react";
import { useAppContext } from "@/contexts/AppContext";
import { formatINR } from "@/lib/currency";
import { usePagination } from "@/hooks/usePagination";
import { useSorting } from "@/hooks/useSorting";
import { SortableHeader } from "@/components/ui/sortable-header";
import { InventoryItem } from "@/lib/data";
import InventoryDialog from "@/components/Inventory/InventoryDialog";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { toast } from "@/components/ui/sonner";

const Inventory = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | undefined>(undefined);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<InventoryItem | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { inventory, refreshInventory, deleteInventoryItem } = useAppContext();

  // Filter inventory based on search query
  const filteredInventory = inventory.filter((item) => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase();
    return (
      item.name.toLowerCase().includes(query) ||
      item.category.toLowerCase().includes(query) ||
      item.id.toLowerCase().includes(query)
    );
  });

  // Sorting
  const {
    items: sortedInventory,
    sortConfig,
    requestSort
  } = useSorting<InventoryItem>(filteredInventory, 'quantity', 'asc');

  // Pagination
  const PAGE_SIZE = 10;
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedInventory,
    goToPage,
    totalItems
  } = usePagination({
    data: sortedInventory,
    pageSize: PAGE_SIZE,
    initialPage: 1,
  });

  // Column definitions for the table
  const columns = [
    { key: 'id' as keyof InventoryItem, label: 'ID' },
    { key: 'name' as keyof InventoryItem, label: 'Name' },
    { key: 'category' as keyof InventoryItem, label: 'Category' },
    { key: 'price' as keyof InventoryItem, label: 'Price', className: 'text-right' },
    { key: 'cost' as keyof InventoryItem, label: 'Cost', className: 'text-right' },
    { key: 'quantity' as keyof InventoryItem, label: 'Quantity', className: 'text-right' },
  ];

  // Refresh inventory data
  const handleRefresh = () => {
    refreshInventory();
  };

  // Handle opening the dialog for creating a new item
  const handleAddItem = () => {
    setSelectedItem(undefined);
    setIsDialogOpen(true);
  };

  // Handle opening the dialog for editing an existing item
  const handleEditItem = (item: InventoryItem) => {
    setSelectedItem(item);
    setIsDialogOpen(true);
  };

  // Handle successful form submission
  const handleFormSuccess = () => {
    refreshInventory();
  };

  const handleDeleteClick = (item: InventoryItem) => {
    setItemToDelete(item);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteItem = async () => {
    if (!itemToDelete) return;

    try {
      setIsDeleting(true);
      await deleteInventoryItem(itemToDelete.id);
      setIsDeleteDialogOpen(false);
      setItemToDelete(null);
    } catch (error) {
      console.error("Error deleting inventory item:", error);
      // Error is already handled by the context with toast
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  return (
    <div className="flex min-h-screen">
      <Sidebar isOpen={sidebarOpen} toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? "md:ml-64" : "md:ml-16"}`}>
        <Header toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isOpen={sidebarOpen} />

        <main className="px-4 py-6 md:px-6">
          <div className="flex flex-col gap-4 mb-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <h1 className="text-2xl font-bold">Inventory Management</h1>
              <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                <div className="relative w-full sm:w-[300px]">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search inventory..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Button onClick={handleAddItem}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">Add Item</span>
                  <span className="sm:hidden">Add</span>
                </Button>
                <Button variant="outline" onClick={handleRefresh}>
                  <RefreshCcw className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">Refresh</span>
                  <span className="sm:hidden">Refresh</span>
                </Button>
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              Showing {paginatedInventory.length} of {totalItems} items
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Parts & Accessories</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Desktop view - Table */}
              <div className="hidden md:block overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      {columns.map(column => (
                        <SortableHeader
                          key={String(column.key)}
                          column={column}
                          sortConfig={sortConfig}
                          onSort={requestSort}
                        />
                      ))}
                      <th className="py-3 px-4 text-center">Status</th>
                      <th className="py-3 px-4 text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedInventory.length === 0 ? (
                      <tr>
                        <td colSpan={8} className="text-center py-8">
                          No inventory items found.
                        </td>
                      </tr>
                    ) : (
                      paginatedInventory.map(item => (
                        <tr key={item.id} className="border-b hover:bg-muted/50">
                          <td className="py-3 px-4">{item.id.substring(0, 8)}</td>
                          <td className="py-3 px-4 font-medium">{item.name}</td>
                          <td className="py-3 px-4">{item.category}</td>
                          <td className="py-3 px-4 text-right">{formatINR(item.price)}</td>
                          <td className="py-3 px-4 text-right">{formatINR(item.cost)}</td>
                          <td className="py-3 px-4 text-right">{item.quantity}</td>
                          <td className="py-3 px-4 text-center">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              item.quantity <= item.threshold
                              ? 'bg-red-100 text-red-700'
                              : 'bg-green-100 text-green-700'
                            }`}>
                              {item.quantity <= item.threshold ? 'Low Stock' : 'In Stock'}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-center">
                            <div className="flex justify-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditItem(item)}
                                className="h-8 w-8 p-0"
                              >
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                onClick={() => handleDeleteClick(item)}
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* Mobile view - Cards */}
              <div className="md:hidden space-y-4">
                {paginatedInventory.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No inventory items found.
                  </div>
                ) : (
                  paginatedInventory.map(item => (
                    <div key={item.id} className="border rounded-lg p-4 hover:bg-muted/50">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-xs text-muted-foreground">
                            ID: {item.id.substring(0, 8)}
                          </p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          item.quantity <= item.threshold
                          ? 'bg-red-100 text-red-700'
                          : 'bg-green-100 text-green-700'
                        }`}>
                          {item.quantity <= item.threshold ? 'Low Stock' : 'In Stock'}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-1 text-sm mb-3">
                        <div>
                          <span className="text-muted-foreground">Category:</span> {item.category}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Quantity:</span> {item.quantity}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Price:</span> {formatINR(item.price)}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Cost:</span> {formatINR(item.cost)}
                        </div>
                      </div>

                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditItem(item)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-destructive hover:text-destructive"
                          onClick={() => handleDeleteClick(item)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4">
                  <p className="text-sm text-muted-foreground">
                    Showing {Math.min((currentPage - 1) * PAGE_SIZE + 1, totalItems)} to {Math.min(currentPage * PAGE_SIZE, totalItems)} of {totalItems} items
                  </p>

                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            if (currentPage > 1) goToPage(currentPage - 1);
                          }}
                          aria-disabled={currentPage === 1}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>

                      {Array.from({ length: totalPages }).map((_, i) => {
                        const page = i + 1;
                        // Show first page, last page, and pages around current page
                        if (
                          page === 1 ||
                          page === totalPages ||
                          (page >= currentPage - 1 && page <= currentPage + 1)
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  goToPage(page);
                                }}
                                isActive={page === currentPage}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        }

                        // Show ellipsis
                        if (
                          (page === 2 && currentPage > 3) ||
                          (page === totalPages - 1 && currentPage < totalPages - 2)
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationEllipsis />
                            </PaginationItem>
                          );
                        }

                        return null;
                      })}

                      <PaginationItem>
                        <PaginationNext
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            if (currentPage < totalPages) goToPage(currentPage + 1);
                          }}
                          aria-disabled={currentPage === totalPages}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </div>

      <InventoryDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        item={selectedItem}
        onSuccess={handleFormSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Inventory Item</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{itemToDelete?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center gap-2 p-4 bg-amber-50 border border-amber-200 rounded-md">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            <p className="text-sm text-amber-700">
              Inventory items used in repair jobs or invoices cannot be deleted. You must remove them from those records first.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelDelete}>Cancel</Button>
            <Button
              variant="destructive"
              onClick={handleDeleteItem}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Item'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Inventory;
