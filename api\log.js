// Simple API endpoint to log events to the server console
// This will show up in Vercel logs

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }
  
  try {
    const { type, data } = req.body;
    
    // Log the event to the server console
    console.log(`[SERVER LOG] ${type}:`, JSON.stringify(data, null, 2));
    
    // Return a success response
    return res.status(200).json({ 
      success: true, 
      message: 'Event logged successfully',
      eventId: data.eventId
    });
  } catch (error) {
    console.error('Error logging event:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error logging event',
      error: error.message
    });
  }
}
