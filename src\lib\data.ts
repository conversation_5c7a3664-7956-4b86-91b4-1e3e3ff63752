
export type Customer = {
  id: string;
  name: string;
  email?: string;
  phone: string;
  address?: string;
  createdAt: Date;
  displayId?: string; // Human-readable ID like C1001
};

export type RepairStatus = 'pending' | 'inProgress' | 'completed' | 'delivered' | 'cancelled';

export type RepairJob = {
  id: string;           // Display ID (e.g., R123)
  dbId?: string;        // Actual database ID (UUID)
  customerId: string;
  customerName: string;
  deviceType: string;
  brand: string;
  model: string;
  serialNumber?: string;
  issueDescription: string;
  technicianId?: string;
  technicianName?: string;
  status: RepairStatus;
  estimatedCost?: number;
  finalCost?: number;
  partsUsed?: Array<{id: string, name: string, cost: number, quantity: number}>;
  notes?: string[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  deliveredAt?: Date;
};

export type InventoryItem = {
  id: string;
  name: string;
  category: string;
  price: number;
  cost: number;
  quantity: number;
  threshold: number;
  description?: string;
};

export type InvoiceItem = {
  id?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  itemType: 'part' | 'labor' | 'service' | 'other';
  partId?: string; // Reference to inventory item if applicable
  discountAmount?: number;
  discountType?: 'percentage' | 'fixed';
  notes?: string;
};

export type Payment = {
  id: string;
  invoiceId: string;
  amount: number;
  paymentMethod: 'cash' | 'card' | 'upi' | 'bank';
  paymentDate: Date;
  notes?: string;
  receiptNumber: string; // Required field for tracking payments
};

export type Invoice = {
  id: string;
  invoiceNumber: string; // Business-friendly number (e.g., INV-2023-001)
  repairJobId: string;
  customerId: string;
  customerName: string;
  items: InvoiceItem[];
  subtotal: number;
  discountAmount?: number;
  discountType?: 'percentage' | 'fixed';
  total: number;
  paidAmount: number;
  balance: number;
  paymentStatus: 'paid' | 'partial' | 'pending' | 'overdue';
  payments?: Payment[];
  paymentMethod?: 'cash' | 'card' | 'upi' | 'bank'; // For creating new invoices
  notes?: string;
  terms?: string;
  createdAt: Date;
  dueDate?: Date;
  createdBy?: string; // User who created the invoice
};

// Mock data removed - application uses database-fetched data only
export const customers: Customer[] = [];

// Mock data removed - application uses database-fetched data only
export const repairJobs: RepairJob[] = [];

// Mock data removed - application uses database-fetched data only
export const inventory: InventoryItem[] = [];

// Mock data removed - application uses database-fetched data only
export const payments: Payment[] = [];

// Mock Invoices - Note: In production, invoices are fetched from database with proper UUIDs
// This mock data is only used for development/testing when database is not available
// The problematic mock data with human-readable IDs has been removed to prevent UUID errors
export const invoices: Invoice[] = [
  // Mock data removed - application should use database-fetched invoices with proper UUIDs
  // The getInvoices() service function handles proper data mapping from database
];

// Statistics calculation functions - DEPRECATED
// These functions are deprecated and should not be used
// Use the service functions in src/services/dashboard.ts instead
export const getRepairStatusCounts = () => {
  console.warn("getRepairStatusCounts is deprecated. Use getDashboardData from services/dashboard.ts");
  return {
    pending: 0,
    inProgress: 0,
    completed: 0,
    delivered: 0,
    cancelled: 0
  };
};

export const getRevenueSummary = () => {
  console.warn("getRevenueSummary is deprecated. Use getDashboardData from services/dashboard.ts");
  return {
    today: 0,
    thisWeek: 0,
    thisMonth: 0,
    pending: 0
  };
};

export const getInventoryAlerts = () => {
  console.warn("getInventoryAlerts is deprecated. Use getDashboardData from services/dashboard.ts");
  return [];
};

export const getRecentRepairs = (_count = 5) => {
  console.warn("getRecentRepairs is deprecated. Use getDashboardData from services/dashboard.ts");
  return [];
};
