import { supabase } from "@/integrations/supabase/client";

export interface CompanySettings {
  companyName: string;
  phone: string;
  email: string;
  address: string;
  logo?: string;
}

export interface ApplicationSettings {
  currency: string;
  darkMode: boolean;
  smsNotifications: boolean;
}

export interface Settings {
  id?: string;
  company: CompanySettings;
  application: ApplicationSettings;
  updatedAt?: string;
}

export const DEFAULT_SETTINGS: Settings = {
  company: {
    companyName: "Mobile Repair Shop",
    phone: "+91 1234567890",
    email: "<EMAIL>",
    address: "123 Repair Street, Tech City, India"
  },
  application: {
    currency: "INR",
    darkMode: false,
    smsNotifications: true
  }
};

/**
 * Get settings from the database
 * @returns Promise<Settings>
 */
export async function getSettings(): Promise<Settings> {
  try {
    // Get settings from database
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.error("Error fetching settings:", error);
      return DEFAULT_SETTINGS;
    }

    if (!data) {
      return DEFAULT_SETTINGS;
    }

    // Transform database format to application format
    return {
      id: data.id,
      company: {
        companyName: data.company_name || DEFAULT_SETTINGS.company.companyName,
        phone: data.phone || DEFAULT_SETTINGS.company.phone,
        email: data.email || DEFAULT_SETTINGS.company.email,
        address: data.address || DEFAULT_SETTINGS.company.address,
        gstin: data.gstin || DEFAULT_SETTINGS.company.gstin,
        logo: data.logo || undefined
      },
      application: {
        currency: data.currency || DEFAULT_SETTINGS.application.currency,
        gstRate: data.gst_rate || DEFAULT_SETTINGS.application.gstRate,
        darkMode: data.dark_mode || DEFAULT_SETTINGS.application.darkMode,
        smsNotifications: data.sms_notifications || DEFAULT_SETTINGS.application.smsNotifications
      },
      updatedAt: data.updated_at
    };
  } catch (error) {
    console.error("Unexpected error fetching settings:", error);
    return DEFAULT_SETTINGS;
  }
}

/**
 * Save settings to the database
 * @param settings Settings to save
 * @returns Promise<Settings>
 */
export async function saveSettings(settings: Settings): Promise<Settings> {
  try {
    // Transform application format to database format
    const dbSettings = {
      company_name: settings.company.companyName,
      phone: settings.company.phone,
      email: settings.company.email,
      address: settings.company.address,
      gstin: settings.company.gstin,
      logo: settings.company.logo,
      currency: settings.application.currency,
      gst_rate: settings.application.gstRate,
      dark_mode: settings.application.darkMode,
      sms_notifications: settings.application.smsNotifications,
      updated_at: new Date().toISOString()
    };

    // Insert or update settings
    const { error } = await supabase
      .from('settings')
      .upsert({
        id: settings.id || '1', // Use ID if provided, otherwise use '1'
        ...dbSettings
      })
      .select()
      .single();

    if (error) {
      console.error("Error saving settings:", error);
      throw new Error("Failed to save settings: " + error.message);
    }

    // Toast message will be shown by the component

    // Return updated settings
    return await getSettings();
  } catch (error) {
    console.error("Unexpected error saving settings:", error);
    throw error;
  }
}


