
import { supabase } from "@/integrations/supabase/client";
import type { InventoryItem } from "@/lib/data";

export async function getInventoryItems(): Promise<InventoryItem[]> {
  const { data, error } = await supabase
    .from('inventory_items')
    .select('*');
  
  if (error) {
    console.error("Error fetching inventory items:", error);
    throw error;
  }
  
  return data.map(item => ({
    id: item.id,
    name: item.name,
    category: item.category,
    price: Number(item.price),
    cost: Number(item.cost),
    quantity: item.quantity,
    threshold: item.threshold,
    description: item.description || undefined
  }));
}

export async function getInventoryItemById(id: string): Promise<InventoryItem | null> {
  const { data, error } = await supabase
    .from('inventory_items')
    .select('*')
    .eq('id', id)
    .single();
  
  if (error) {
    if (error.code === 'PGRST116') {
      return null; // No inventory item found
    }
    console.error("Error fetching inventory item:", error);
    throw error;
  }
  
  return {
    id: data.id,
    name: data.name,
    category: data.category,
    price: Number(data.price),
    cost: Number(data.cost),
    quantity: data.quantity,
    threshold: data.threshold,
    description: data.description || undefined
  };
}

export async function createInventoryItem(item: Omit<InventoryItem, "id">): Promise<InventoryItem> {
  const { data, error } = await supabase
    .from('inventory_items')
    .insert({
      name: item.name,
      category: item.category,
      price: item.price,
      cost: item.cost,
      quantity: item.quantity,
      threshold: item.threshold,
      description: item.description
    })
    .select()
    .single();
  
  if (error) {
    console.error("Error creating inventory item:", error);
    throw error;
  }
  
  return {
    id: data.id,
    name: data.name,
    category: data.category,
    price: Number(data.price),
    cost: Number(data.cost),
    quantity: data.quantity,
    threshold: data.threshold,
    description: data.description || undefined
  };
}

export async function updateInventoryItem(item: InventoryItem): Promise<InventoryItem> {
  const { data, error } = await supabase
    .from('inventory_items')
    .update({
      name: item.name,
      category: item.category,
      price: item.price,
      cost: item.cost,
      quantity: item.quantity,
      threshold: item.threshold,
      description: item.description
    })
    .eq('id', item.id)
    .select()
    .single();
  
  if (error) {
    console.error("Error updating inventory item:", error);
    throw error;
  }
  
  return {
    id: data.id,
    name: data.name,
    category: data.category,
    price: Number(data.price),
    cost: Number(data.cost),
    quantity: data.quantity,
    threshold: data.threshold,
    description: data.description || undefined
  };
}

export async function deleteInventoryItem(id: string): Promise<boolean> {
  try {
    if (!id) throw new Error("Inventory item ID is required");

    // Check if inventory item is used in any repair jobs
    const { data: partsUsed, error: partsError } = await supabase
      .from('parts_used')
      .select('id')
      .eq('inventory_item_id', id);

    if (partsError) {
      console.error("Error checking for parts usage:", partsError);
      throw new Error(`Failed to check for parts usage: ${partsError.message}`);
    }

    // If inventory item is used in repairs, don't allow deletion
    if (partsUsed && partsUsed.length > 0) {
      throw new Error(`Cannot delete inventory item that is used in repair jobs. Please remove it from repairs first.`);
    }

    // Check if inventory item is used in any invoice items
    const { data: invoiceItems, error: invoiceError } = await supabase
      .from('invoice_items')
      .select('id')
      .eq('part_id', id);

    if (invoiceError) {
      console.error("Error checking for invoice usage:", invoiceError);
      throw new Error(`Failed to check for invoice usage: ${invoiceError.message}`);
    }

    // If inventory item is used in invoices, don't allow deletion
    if (invoiceItems && invoiceItems.length > 0) {
      throw new Error(`Cannot delete inventory item that is used in invoices. Please remove it from invoices first.`);
    }

    // Delete the inventory item
    const { error } = await supabase
      .from('inventory_items')
      .delete()
      .eq('id', id);

    if (error) {
      console.error("Error deleting inventory item:", error);
      throw new Error(`Failed to delete inventory item: ${error.message}`);
    }

    console.log("Inventory item deleted successfully:", id);
    return true;
  } catch (error) {
    console.error("Unexpected error in deleteInventoryItem:", error);
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred while deleting inventory item");
  }
}
