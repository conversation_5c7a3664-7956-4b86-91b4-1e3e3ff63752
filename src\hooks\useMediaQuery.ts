import { useState, useEffect } from 'react';

/**
 * A hook that returns true if the current viewport matches the provided media query
 * 
 * @param query The media query to check
 * @returns Boolean indicating if the media query matches
 */
export function useMediaQuery(query: string): boolean {
  // Initialize with the current match state
  const getMatches = (): boolean => {
    // Check if we're in the browser environment
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  };

  const [matches, setMatches] = useState<boolean>(getMatches());

  // Handle the change event
  function handleChange() {
    setMatches(getMatches());
  }

  useEffect(() => {
    const matchMedia = window.matchMedia(query);

    // Listen for changes to the media query
    // Modern browsers
    if (matchMedia.addEventListener) {
      matchMedia.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      matchMedia.addListener(handleChange);
    }

    // Update state on mount in case the media query already matches
    handleChange();

    // Clean up
    return () => {
      if (matchMedia.removeEventListener) {
        matchMedia.removeEventListener('change', handleChange);
      } else {
        matchMedia.removeListener(handleChange);
      }
    };
  }, [query]);

  return matches;
}

/**
 * Predefined breakpoints for common screen sizes
 */
export const breakpoints = {
  sm: '(min-width: 640px)',
  md: '(min-width: 768px)',
  lg: '(min-width: 1024px)',
  xl: '(min-width: 1280px)',
  '2xl': '(min-width: 1536px)',
  // Dark mode
  dark: '(prefers-color-scheme: dark)',
  // Reduced motion
  reducedMotion: '(prefers-reduced-motion: reduce)',
  // Print
  print: '(min-width: 0px)',
};

/**
 * Hooks for common breakpoints
 */
export function useIsMobile() {
  return !useMediaQuery(breakpoints.md);
}

export function useIsTablet() {
  return useMediaQuery(breakpoints.md) && !useMediaQuery(breakpoints.lg);
}

export function useIsDesktop() {
  return useMediaQuery(breakpoints.lg);
}

export function useIsDarkMode() {
  return useMediaQuery(breakpoints.dark);
}

export function usePrefersReducedMotion() {
  return useMediaQuery(breakpoints.reducedMotion);
}

export function useIsPrinting() {
  return useMediaQuery(breakpoints.print);
}
