import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search, Plus } from "lucide-react";
import { useAppContext } from "@/contexts/AppContext";
import { InventoryItem } from "@/lib/data";
import { formatINR } from "@/lib/currency";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface InventoryItemSelectorProps {
  onItemSelected: (item: { description: string; quantity: string; unitPrice: string; itemType: "part" }) => void;
  onClose: () => void;
}

const InventoryItemSelector: React.FC<InventoryItemSelectorProps> = ({ 
  onItemSelected,
  onClose
}) => {
  const { inventory, refreshInventory } = useAppContext();
  const [searchQuery, setSearchQuery] = useState("");

  // Filter inventory based on search query
  const filteredInventory = inventory.filter((item) => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      item.name.toLowerCase().includes(query) ||
      item.category.toLowerCase().includes(query)
    );
  });

  // Add an item to the invoice
  const addItem = (item: InventoryItem) => {
    onItemSelected({
      description: item.name,
      quantity: "1",
      unitPrice: item.price.toString(),
      itemType: "part"
    });
    onClose();
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Select Item from Inventory</h3>
        <Button 
          type="button" 
          variant="outline" 
          size="sm"
          onClick={() => refreshInventory()}
        >
          Refresh Inventory
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search inventory..."
          className="pl-8"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <ScrollArea className="h-[300px] border rounded-md p-4">
        <div className="space-y-2">
          {filteredInventory.length === 0 ? (
            <p className="text-center text-muted-foreground py-4">
              No inventory items found.
            </p>
          ) : (
            filteredInventory.map((item) => (
              <div
                key={item.id}
                className="flex justify-between items-center p-2 hover:bg-muted/50 rounded-md"
              >
                <div>
                  <p className="font-medium">{item.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {item.category} • {formatINR(item.price)} • {item.quantity} in stock
                  </p>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addItem(item)}
                  disabled={item.quantity <= 0}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  Add
                </Button>
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      <div className="flex justify-end">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
      </div>
    </div>
  );
};

export const InventoryItemSelectorDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onItemSelected: (item: { description: string; quantity: string; unitPrice: string; itemType: "part" }) => void;
}> = ({ open, onOpenChange, onItemSelected }) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Inventory Item</DialogTitle>
        </DialogHeader>
        <InventoryItemSelector 
          onItemSelected={onItemSelected} 
          onClose={() => onOpenChange(false)} 
        />
      </DialogContent>
    </Dialog>
  );
};

export default InventoryItemSelector;
