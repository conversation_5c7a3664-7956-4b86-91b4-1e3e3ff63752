// This script populates the Supabase database with mock data
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

// Supabase connection details
const SUPABASE_URL = "https://jjbmfyppkfowgjywpryb.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpqYm1meXBwa2Zvd2dqeXdwcnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4MDM5NjYsImV4cCI6MjA2MDM3OTk2Nn0.u_bJ3pz5c1emdOXlvdabD1OntjaDRlguJh7fpQPOBbI";

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Generate UUIDs for our entities
const customerIds = {
  C1001: uuidv4(),
  C1002: uuidv4(),
  C1003: uuidv4(),
  C1004: uuidv4(),
  C1005: uuidv4()
};

const repairJobIds = {
  R2001: uuidv4(),
  R2002: uuidv4(),
  R2003: uuidv4(),
  R2004: uuidv4(),
  R2005: uuidv4(),
  R2006: uuidv4(),
  R2007: uuidv4()
};

const inventoryItemIds = {
  P101: uuidv4(),
  P102: uuidv4(),
  P201: uuidv4(),
  P202: uuidv4(),
  P301: uuidv4()
};

const invoiceIds = {
  INV1001: uuidv4(),
  INV1002: uuidv4()
};

// Mock data
const customers = [
  {
    id: customerIds.C1001,
    name: "John Doe",
    email: "<EMAIL>",
    phone: "555-1234",
    address: "123 Main St, Anytown",
    created_at: new Date("2023-01-15").toISOString()
  },
  {
    id: customerIds.C1002,
    name: "Jane Smith",
    email: "<EMAIL>",
    phone: "555-5678",
    address: "456 Oak Ave, Somecity",
    created_at: new Date("2023-02-20").toISOString()
  },
  {
    id: customerIds.C1003,
    name: "Robert Johnson",
    email: "<EMAIL>",
    phone: "555-9012",
    created_at: new Date("2023-03-10").toISOString()
  },
  {
    id: customerIds.C1004,
    name: "Sarah Williams",
    email: "<EMAIL>",
    phone: "555-3456",
    address: "789 Pine Rd, Elsewhere",
    created_at: new Date("2023-04-05").toISOString()
  },
  {
    id: customerIds.C1005,
    name: "Michael Brown",
    email: "<EMAIL>",
    phone: "555-7890",
    created_at: new Date("2023-05-12").toISOString()
  }
];

const repairJobs = [
  {
    id: repairJobIds.R2001,
    customer_id: customerIds.C1001,
    device_type: "Smartphone",
    brand: "Apple",
    model: "iPhone 12",
    serial_number: "IMEI12345678",
    issue_description: "Cracked screen",
    technician_id: "T001",
    technician_name: "Alex Tech",
    status: "completed",
    estimated_cost: 8500,
    final_cost: 9200,
    notes: ["Screen replaced", "Phone tested and working properly"],
    created_at: new Date("2023-06-10").toISOString(),
    updated_at: new Date("2023-06-12").toISOString(),
    completed_at: new Date("2023-06-12").toISOString()
  },
  {
    id: repairJobIds.R2002,
    customer_id: customerIds.C1002,
    device_type: "Smartphone",
    brand: "Samsung",
    model: "Galaxy S21",
    issue_description: "Battery draining quickly",
    technician_id: "T002",
    technician_name: "Maya Tech",
    status: "inProgress",
    estimated_cost: 2500,
    notes: ["Battery replacement in progress"],
    created_at: new Date("2023-06-14").toISOString(),
    updated_at: new Date("2023-06-15").toISOString()
  },
  {
    id: repairJobIds.R2003,
    customer_id: customerIds.C1003,
    device_type: "Tablet",
    brand: "Apple",
    model: "iPad Pro",
    serial_number: "IPAD98765432",
    issue_description: "Not charging",
    status: "pending",
    estimated_cost: 3000,
    created_at: new Date("2023-06-16").toISOString(),
    updated_at: new Date("2023-06-16").toISOString()
  },
  {
    id: repairJobIds.R2004,
    customer_id: customerIds.C1004,
    device_type: "Smartphone",
    brand: "Google",
    model: "Pixel 6",
    issue_description: "Camera not working",
    technician_id: "T001",
    technician_name: "Alex Tech",
    status: "delivered",
    estimated_cost: 4500,
    final_cost: 4500,
    notes: ["Camera module replaced", "All features tested"],
    created_at: new Date("2023-06-08").toISOString(),
    updated_at: new Date("2023-06-11").toISOString(),
    completed_at: new Date("2023-06-11").toISOString(),
    delivered_at: new Date("2023-06-13").toISOString()
  },
  {
    id: repairJobIds.R2005,
    customer_id: customerIds.C1005,
    device_type: "Smartphone",
    brand: "Apple",
    model: "iPhone 11",
    issue_description: "Speaker not working",
    technician_id: "T002",
    technician_name: "Maya Tech",
    status: "cancelled",
    estimated_cost: 2000,
    notes: ["Customer decided not to proceed with repair"],
    created_at: new Date("2023-06-12").toISOString(),
    updated_at: new Date("2023-06-14").toISOString()
  },
  {
    id: repairJobIds.R2006,
    customer_id: customerIds.C1001,
    device_type: "Tablet",
    brand: "Samsung",
    model: "Galaxy Tab S7",
    issue_description: "Wi-Fi connectivity issues",
    technician_id: "T001",
    technician_name: "Alex Tech",
    status: "inProgress",
    estimated_cost: 1800,
    notes: ["Diagnosed as antenna issue", "Replacement part ordered"],
    created_at: new Date("2023-06-15").toISOString(),
    updated_at: new Date("2023-06-16").toISOString()
  },
  {
    id: repairJobIds.R2007,
    customer_id: customerIds.C1002,
    device_type: "Smartphone",
    brand: "Apple",
    model: "iPhone 13",
    serial_number: "IMEI87654321",
    issue_description: "Battery replacement needed",
    technician_id: "T002",
    technician_name: "Maya Tech",
    status: "completed",
    estimated_cost: 4500,
    final_cost: 4800,
    notes: ["Battery replaced", "Device tested for 24 hours"],
    created_at: new Date("2023-06-18").toISOString(),
    updated_at: new Date("2023-06-20").toISOString(),
    completed_at: new Date("2023-06-20").toISOString()
  }
];

const inventoryItems = [
  {
    id: inventoryItemIds.P101,
    name: "iPhone 12 Screen",
    category: "Screens",
    price: 9999,
    cost: 6500,
    quantity: 8,
    threshold: 3,
    description: "Original quality replacement screen for iPhone 12",
    created_at: new Date().toISOString()
  },
  {
    id: inventoryItemIds.P102,
    name: "iPhone 13 Screen",
    category: "Screens",
    price: 12999,
    cost: 8500,
    quantity: 5,
    threshold: 3,
    description: "Original quality replacement screen for iPhone 13",
    created_at: new Date().toISOString()
  },
  {
    id: inventoryItemIds.P201,
    name: "Samsung S21 Battery",
    category: "Batteries",
    price: 3999,
    cost: 1500,
    quantity: 12,
    threshold: 5,
    description: "Original Samsung battery for Galaxy S21",
    created_at: new Date().toISOString()
  },
  {
    id: inventoryItemIds.P202,
    name: "iPhone 12 Battery",
    category: "Batteries",
    price: 3499,
    cost: 1200,
    quantity: 2,
    threshold: 5,
    description: "High-quality replacement battery for iPhone 12",
    created_at: new Date().toISOString()
  },
  {
    id: inventoryItemIds.P301,
    name: "Pixel 6 Camera Module",
    category: "Cameras",
    price: 6999,
    cost: 3000,
    quantity: 4,
    threshold: 2,
    description: "Original camera module for Google Pixel 6",
    created_at: new Date().toISOString()
  }
];

const partsUsed = [
  {
    id: uuidv4(),
    repair_job_id: repairJobIds.R2001,
    inventory_item_id: inventoryItemIds.P101,
    quantity: 1,
    cost: 6500,
    created_at: new Date("2023-06-11").toISOString()
  },
  {
    id: uuidv4(),
    repair_job_id: repairJobIds.R2004,
    inventory_item_id: inventoryItemIds.P301,
    quantity: 1,
    cost: 3000,
    created_at: new Date("2023-06-10").toISOString()
  },
  {
    id: uuidv4(),
    repair_job_id: repairJobIds.R2007,
    inventory_item_id: inventoryItemIds.P202,
    quantity: 1,
    cost: 1200,
    created_at: new Date("2023-06-19").toISOString()
  }
];

const invoices = [
  {
    id: invoiceIds.INV1001,
    repair_job_id: repairJobIds.R2001,
    customer_id: customerIds.C1001,
    labor_cost: 1500,
    subtotal: 11499,
    tax: 1150,
    total: 12649,
    paid_amount: 12649,
    balance: 0,
    payment_status: "paid",
    payment_method: "card",
    created_at: new Date("2023-06-12").toISOString()
  },
  {
    id: invoiceIds.INV1002,
    repair_job_id: repairJobIds.R2004,
    customer_id: customerIds.C1004,
    labor_cost: 1000,
    subtotal: 7999,
    tax: 800,
    total: 8799,
    paid_amount: 5000,
    balance: 3799,
    payment_status: "partial",
    payment_method: "cash",
    created_at: new Date("2023-06-11").toISOString(),
    due_date: new Date("2023-06-25").toISOString()
  }
];

const invoiceItems = [
  {
    id: uuidv4(),
    invoice_id: invoiceIds.INV1001,
    description: "iPhone 12 Screen Replacement",
    quantity: 1,
    unit_price: 9999,
    total: 9999,
    created_at: new Date("2023-06-12").toISOString()
  },
  {
    id: uuidv4(),
    invoice_id: invoiceIds.INV1002,
    description: "Pixel 6 Camera Module Replacement",
    quantity: 1,
    unit_price: 6999,
    total: 6999,
    created_at: new Date("2023-06-11").toISOString()
  }
];

// Function to insert data into a table
async function insertData(tableName, data) {
  console.log(`Inserting data into ${tableName}...`);
  const { data: result, error } = await supabase
    .from(tableName)
    .upsert(data, { onConflict: 'id' });

  if (error) {
    console.error(`Error inserting data into ${tableName}:`, error);
    return false;
  }

  console.log(`Successfully inserted data into ${tableName}`);
  return true;
}

// Main function to populate all tables
async function populateDatabase() {
  try {
    console.log('Starting database population...');

    // Insert data in the correct order to respect foreign key constraints
    await insertData('customers', customers);
    await insertData('inventory_items', inventoryItems);
    await insertData('repair_jobs', repairJobs);
    await insertData('parts_used', partsUsed);
    await insertData('invoices', invoices);
    await insertData('invoice_items', invoiceItems);

    console.log('Database population completed successfully!');
  } catch (error) {
    console.error('Error populating database:', error);
  }
}

// Run the population script
populateDatabase();
