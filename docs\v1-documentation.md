# Mobile Repair Shop Management System - V1 Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [System Overview](#system-overview)
3. [Technical Architecture](#technical-architecture)
4. [Core Features](#core-features)
   - [Dashboard](#dashboard)
   - [Customer Management](#customer-management)
   - [Repair Management](#repair-management)
   - [Inventory Management](#inventory-management)
   - [Invoice & Payment](#invoice--payment)
   - [Reports](#reports)
   - [Settings](#settings)
5. [Database Structure](#database-structure)
6. [User Interface](#user-interface)
7. [Error Logging & Monitoring](#error-logging--monitoring)
8. [Installation & Setup](#installation--setup)
9. [Deployment](#deployment)
10. [Troubleshooting](#troubleshooting)
11. [Future Enhancements](#future-enhancements)

## Introduction

The Mobile Repair Shop Management System is a comprehensive web application designed for small mobile phone repair shops with 2-3 technicians, 1 owner, and 1 front desk staff. The system streamlines the entire repair workflow from customer registration to invoice generation, helping repair shop owners manage their business efficiently.

This documentation covers Version 1 of the system, which includes all core functionality needed to run a repair shop effectively.

## System Overview

The Mobile Repair Shop Management System is built as a single-page application (SPA) with a modern, responsive interface. It provides a complete solution for:

- Managing customer information
- Tracking repair jobs through their lifecycle
- Managing inventory of parts and accessories
- Generating invoices and tracking payments
- Printing repair tickets for thermal printers
- Generating business reports and analytics

The system is designed to be intuitive and efficient, with a focus on the specific needs of small repair shops.

## Technical Architecture

### Technology Stack

- **Frontend**: React with TypeScript
- **UI Framework**: Tailwind CSS with shadcn/ui components
- **Build Tool**: Vite
- **State Management**: React Context API and React Query
- **Routing**: React Router
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Planned for V2
- **Testing**: Playwright

### Key Libraries

- **@tanstack/react-query**: For data fetching, caching, and state management
- **date-fns**: For date manipulation and formatting
- **lucide-react**: For icons
- **react-to-print**: For printing functionality
- **recharts**: For data visualization
- **sonner**: For toast notifications
- **zod**: For form validation
- **react-helmet**: For managing document head
- **@vercel/analytics**: For analytics and error tracking (when available)

## Core Features

### Dashboard

The dashboard provides a quick overview of the repair shop's operations with:

- **Repair Status Summary**: Count of repairs by status (pending, in progress, completed, delivered)
- **Revenue Summary**: Daily, weekly, and monthly revenue figures
- **Inventory Alerts**: Low stock items that need attention
- **Recent Repairs**: List of recently created or updated repair jobs
- **Repair Status Chart**: Visual representation of repair status distribution

The dashboard also includes a quick access tab to create new repair jobs without navigating away.

### Customer Management

The customer management module allows users to:

- **View Customers**: List all customers with search and pagination
- **Add Customers**: Register new customers with name, phone, and optional email
- **Edit Customers**: Update customer information
- **Delete Customers**: Remove customers from the system
- **View Customer Details**: See comprehensive customer information including repair history
- **Customer Statistics**: View metrics like total repairs, active repairs, and total spent

**Key Implementation Details**:
- Phone numbers must be unique for each customer
- Email field is optional
- Customer IDs are automatically generated with a "C" prefix (e.g., C1001)

### Repair Management

The repair management module is the core of the system, allowing users to:

- **View Repairs**: List all repairs with filtering by status and search
- **Create Repairs**: Register new repair jobs with customer selection, device details, and issue description
- **Update Repair Status**: Track the repair through its lifecycle (pending → in progress → completed → delivered)
- **View Repair Details**: See comprehensive information about a repair job
- **Print Repair Tickets**: Generate printable repair tickets for thermal printers
- **Generate Invoices**: Create invoices for completed repairs

**Repair Statuses**:
- **Pending**: Repair job created but work not yet started
- **In Progress**: Technician is working on the repair
- **Completed**: Repair work is finished but not yet delivered to customer
- **Delivered**: Repair has been delivered to the customer and is considered closed

**Key Implementation Details**:
- Repair status automatically changes to "delivered" when an invoice is fully paid
- Repair tickets can be printed using an alternative printing method that opens a new window
- Estimated cost field is optional
- Invoice status column shows "Invoiced", "Need Invoice", or blank based on repair status
- All columns are sortable for better organization

### Inventory Management

The inventory management module allows users to:

- **View Inventory**: List all inventory items with search and pagination
- **Add Items**: Register new inventory items with name, category, price, cost, and quantity
- **Edit Items**: Update inventory information
- **Low Stock Alerts**: Visual indicators for items below threshold
- **Track Usage**: Items used in repairs are automatically deducted from inventory

**Key Implementation Details**:
- Inventory items have both a selling price and a cost price for profit tracking
- Low stock threshold can be set for each item
- Items can be categorized for better organization

### Invoice & Payment

The invoice and payment module allows users to:

- **Generate Invoices**: Create invoices for completed repairs
- **Add Line Items**: Include parts, labor, and other charges
- **Apply Discounts**: Add percentage or fixed amount discounts
- **Record Payments**: Track full or partial payments
- **Print Invoices**: Generate printable PDF invoices
- **Payment Status Tracking**: Monitor paid, partial, pending, and overdue invoices

**Key Implementation Details**:
- Invoices automatically include repair details and customer information
- Company information from settings is used in all invoice outputs
- Repair status automatically changes to "delivered" when an invoice is fully paid

### Reports

The reports module provides business analytics with:

- **Financial Reports**: Revenue, profit, and tax summaries
- **Repair Reports**: Repair volume, average repair time, and status distribution
- **Inventory Reports**: Stock levels, usage patterns, and value
- **Customer Reports**: Customer acquisition, retention, and spending patterns

**Key Implementation Details**:
- Reports can be filtered by date range
- Data can be sorted by clicking on column headers
- Data can be exported to CSV format with proper formatting
- Visual charts provide quick insights
- Time zone handling is based on the server's time zone

### Settings

The settings module allows customization of:

- **Company Information**: Name, address, contact details, and GST number
- **Application Settings**: Currency, GST rate, and notification preferences
- **Appearance**: Light/dark mode toggle

## Database Structure

The system uses a Supabase PostgreSQL database with the following main tables:

### Customers Table
- `id`: UUID (primary key)
- `display_id`: String (e.g., C1001)
- `name`: String
- `email`: String (nullable)
- `phone`: String
- `address`: String
- `created_at`: Timestamp

### Repair Jobs Table
- `id`: UUID (primary key)
- `customer_id`: UUID (foreign key to customers)
- `device_type`: String
- `brand`: String
- `model`: String
- `serial_number`: String (nullable)
- `issue_description`: Text
- `technician_id`: UUID (nullable, for V2)
- `technician_name`: String (nullable, for V2)
- `status`: Enum ('pending', 'inProgress', 'completed', 'delivered', 'cancelled')
- `estimated_cost`: Numeric (nullable)
- `final_cost`: Numeric (nullable)
- `notes`: Text (nullable)
- `created_at`: Timestamp
- `updated_at`: Timestamp
- `completed_at`: Timestamp (nullable)

### Inventory Items Table
- `id`: UUID (primary key)
- `name`: String
- `category`: String
- `price`: Numeric
- `cost`: Numeric
- `quantity`: Integer
- `threshold`: Integer
- `description`: Text (nullable)

### Invoices Table
- `id`: UUID (primary key)
- `invoice_number`: String
- `repair_job_id`: UUID (foreign key to repair_jobs)
- `customer_id`: UUID (foreign key to customers)
- `labor_cost`: Numeric
- `subtotal`: Numeric
- `discount_amount`: Numeric (nullable)
- `discount_type`: Enum ('percentage', 'fixed') (nullable)
- `tax_rate`: Numeric (deprecated, always 0)
- `tax`: Numeric (deprecated, always 0)
- `total`: Numeric
- `paid_amount`: Numeric
- `balance`: Numeric
- `payment_status`: Enum ('paid', 'partial', 'pending', 'overdue')
- `payment_method`: Enum ('cash', 'card', 'upi', 'bank') (nullable)
- `notes`: Text (nullable)
- `terms`: Text (nullable)
- `due_date`: Timestamp (nullable)
- `created_at`: Timestamp
- `created_by`: String (nullable, for V2)

### Invoice Items Table
- `id`: UUID (primary key)
- `invoice_id`: UUID (foreign key to invoices)
- `item_type`: Enum ('part', 'labor', 'other')
- `name`: String
- `description`: String (nullable)
- `quantity`: Integer
- `unit_price`: Numeric
- `total`: Numeric

### Payments Table
- `id`: UUID (primary key)
- `invoice_id`: UUID (foreign key to invoices)
- `amount`: Numeric
- `payment_method`: Enum ('cash', 'card', 'upi', 'bank')
- `payment_date`: Timestamp
- `notes`: Text (nullable)

### Settings Table
- `id`: UUID (primary key)
- `company`: JSONB (company settings)
- `application`: JSONB (application settings)
- `updated_at`: Timestamp

## User Interface

The user interface is designed to be intuitive and responsive, working well on both desktop and mobile devices.

### Layout Components

- **Header**: Contains the application title, search bar, and user menu
- **Sidebar**: Navigation menu for accessing different modules
- **Main Content Area**: Displays the active module's content
- **Dialogs**: Used for forms, details views, and confirmations
- **Toast Notifications**: Provide feedback on actions

### Responsive Design

- **Desktop View**: Full layout with sidebar and detailed tables
- **Mobile View**: Collapsible sidebar and card-based layouts instead of tables
- **Print View**: Optimized layouts for printing invoices and repair tickets

### Theme

- **Light/Dark Mode**: Supports both light and dark themes
- **Color Scheme**: Uses a consistent color palette throughout the application
- **Typography**: Clear, readable fonts with appropriate sizing

## Error Logging & Monitoring

The system includes a comprehensive error logging and monitoring solution that works even on the free Vercel plan.

### Custom Logging System

- **Client-Side Logging**: Captures and logs errors, warnings, and informational messages from the client
- **Server-Side Logging**: Logs errors and events to Vercel server logs
- **Automatic Capture**: Automatically captures unhandled errors, promise rejections, and page views
- **Page Unload Handling**: Uses `navigator.sendBeacon` and `keepalive` to ensure logs are sent even during page unloads

### Logging Features

- **Severity Levels**: Supports INFO, WARNING, ERROR, and CRITICAL severity levels
- **Contextual Information**: Logs include component name, action, timestamp, and other relevant context
- **Stack Traces**: Captures and logs stack traces for errors
- **Browser Information**: Includes browser and device information for better debugging

### Usage in Code

The logging system can be used throughout the codebase:

```typescript
// Log an informational message
window.customLogger.info('User logged in', { userId: 123 });

// Log a warning
window.customLogger.warn('API rate limit approaching', { endpoint: '/api/data' });

// Log an error
window.customLogger.error('Failed to load data', { error: 'Network error' });

// Log a page view
window.customLogger.pageView('/dashboard');
```

The error logger is also integrated with the application's error handling system, so errors caught by error boundaries or try/catch blocks are automatically logged.

### Viewing Logs

Logs can be viewed in the Vercel dashboard:

1. Go to the Vercel project dashboard
2. Navigate to Deployments > (latest deployment) > Logs
3. Look for logs with `[CUSTOM_LOG]` prefix

## Installation & Setup

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Supabase account

### Installation Steps

1. Clone the repository:
   ```
   git clone <repository-url>
   cd mobile-repair-shop
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Configure environment variables:
   Create a `.env` file with the following variables:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. Start the development server:
   ```
   npm run dev
   ```

5. Build for production:
   ```
   npm run build
   ```

### Database Setup

1. Create a new Supabase project
2. Run the database schema setup scripts (provided separately)
3. Configure RLS (Row Level Security) policies for V2 authentication

## Deployment

The application is designed to be deployed to Vercel, but can be deployed to any static hosting service that supports single-page applications.

### Vercel Deployment

1. **Prerequisites**:
   - A Vercel account (free tier is sufficient)
   - A Git repository with your code (GitHub, GitLab, or Bitbucket)

2. **Deployment Steps**:
   - Log in to your Vercel account
   - Click "Add New" > "Project"
   - Import your Git repository
   - Configure the project:
     - **Framework Preset**: Vite
     - **Build Command**: `npm run build`
     - **Output Directory**: `dist`
     - **Install Command**: `npm install`
   - Add environment variables if needed
   - Click "Deploy"

3. **Vercel Configuration**:
   The repository includes a `vercel.json` file with the following configuration:
   ```json
   {
     "framework": "vite",
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "routes": [
       {
         "src": "/api/(.*)",
         "dest": "/api/$1.js"
       },
       {
         "src": "/custom-logger.js",
         "dest": "/custom-logger.js"
       },
       {
         "src": "/assets/(.*)",
         "dest": "/assets/$1"
       },
       {
         "src": "/(.*)",
         "dest": "/index.html"
       }
     ]
   }
   ```

4. **Custom Domains**:
   - Go to your project settings in Vercel
   - Navigate to "Domains"
   - Add your custom domain
   - Follow the instructions to configure DNS settings

### Other Hosting Options

The application can also be deployed to other static hosting services:

1. **Netlify**:
   - Similar to Vercel, with automatic deployments from Git
   - Create a `netlify.toml` file for configuration

2. **GitHub Pages**:
   - Update the `vite.config.ts` file to set the correct base path
   - Use GitHub Actions for automated deployments

3. **Traditional Hosting**:
   - Build the application with `npm run build`
   - Upload the contents of the `dist` directory to your web server
   - Configure the server to redirect all requests to `index.html`

## Troubleshooting

### Common Issues

1. **MIME Type Errors with Vite Dependencies**
   - Solution: Change base href from '/' to '.' in HTML

2. **Supabase Connection Issues**
   - Check that environment variables are correctly set
   - Verify that Supabase service is running
   - Check network connectivity

3. **Printing Problems**
   - For thermal printer tickets, use the alternative printing method that opens a new window
   - Ensure proper printer drivers are installed

4. **CSV Export Issues**
   - Verify that the data being exported is properly formatted
   - Check for special characters that might cause formatting problems

5. **Logging Issues**
   - If logs are not appearing in Vercel, check that the API endpoint `/api/custom-log` is accessible
   - Verify that the custom logger script is loaded in the HTML
   - Check browser console for any errors related to logging
   - For page refresh issues, the system uses `navigator.sendBeacon` and `keepalive` to ensure logs are sent

## Future Enhancements

The following features are planned for future versions:

### Version 2
- User authentication system with role-based permissions
- Technician management and assignment
- QR code scanning system
- Advanced reporting and analytics
- Multi-branch support
- Enhanced error tracking and monitoring
- Code quality improvements and refactoring
- Performance optimizations

### Version 3
- Customer portal
- Advanced inventory management
- Marketing tools
- Mobile app for technicians

For a complete list of planned features, refer to the TODO.md file in the project repository.

---

This documentation is current as of the V1 release. For updates and additional information, please refer to the project repository.
