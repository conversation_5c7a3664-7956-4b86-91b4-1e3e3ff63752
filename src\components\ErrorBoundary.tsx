import React, { Component, ErrorInfo, ReactNode } from 'react';
import { logError, ErrorSeverity } from '@/utils/errorLogger';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * Error Boundary component to catch and handle React rendering errors
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to our error logging system with minimal info to prevent overflow
    try {
      logError(error, ErrorSeverity.ERROR, {
        component: 'ErrorBoundary',
        action: 'componentDidCatch'
        // Don't include the full component stack to avoid memory issues
      });
    } catch (loggingError) {
      // Last resort fallback if even our error logging fails
      try {
        console.error('[ErrorBoundary] Failed to log error:', error.message);
      } catch (_) {
        // Do nothing if even this fails
      }
    }
  }

  handleReset = (): void => {
    this.setState({ hasError: false, error: null });
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <div className="flex flex-col items-center justify-center min-h-[400px] p-6 text-center">
          <AlertTriangle className="w-12 h-12 text-destructive mb-4" />
          <h2 className="text-2xl font-bold mb-2">Something went wrong</h2>
          <p className="text-muted-foreground mb-6 max-w-md">
            We've encountered an error and it has been logged. Please try again or contact support if the issue persists.
          </p>
          {this.state.error && (
            <div className="bg-muted p-4 rounded-md mb-6 max-w-md overflow-auto text-left">
              <p className="font-mono text-sm">{this.state.error.toString()}</p>
            </div>
          )}
          <Button onClick={this.handleReset}>Try Again</Button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
