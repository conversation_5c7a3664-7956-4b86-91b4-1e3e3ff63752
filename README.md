# Mobile Phone Repair Shop Management System

A comprehensive web application designed for small mobile phone repair shops to streamline their entire business workflow from customer registration to invoice generation.

## 🎯 Project Overview

The Mobile Phone Repair Shop Management System is a modern, responsive single-page application (SPA) built specifically for small repair shops with 2-3 technicians, 1 owner, and 1 front desk staff. The system provides a complete solution for managing customers, tracking repairs, controlling inventory, generating invoices, and analyzing business performance.

### Target Users
- **Small Repair Shops**: 2-3 technicians, 1 owner, 1 front desk staff
- **Mobile Phone Repair Businesses**: Focused on device repair workflow
- **Service Centers**: Looking for comprehensive business management

### Key Benefits
- Streamlined repair workflow management
- Automated invoice generation and payment tracking
- Real-time inventory monitoring with low stock alerts
- Thermal printer support for repair tickets
- Comprehensive business reporting and analytics
- Modern, intuitive user interface

## ✨ Features

### 🏠 Dashboard
- **Real-time Overview**: Repair status counts, revenue summary, low stock alerts
- **Visual Analytics**: Repair status charts and recent repairs display
- **Quick Actions**: Direct access to create new repair jobs
- **Performance Metrics**: Today, weekly, and monthly revenue tracking

### 👥 Customer Management
- **Customer Registration**: Name, phone (unique), optional email, address
- **Customer Database**: Searchable list with pagination and sorting
- **Customer Details**: Comprehensive view with repair and invoice history
- **CRUD Operations**: Create, read, update, and delete customers
- **Customer Analytics**: Total repairs, active repairs, and spending metrics

### 🔧 Repair Job Tracking
- **Repair Lifecycle**: Pending → In Progress → Completed → Delivered
- **Device Information**: Brand, model, serial number, issue description
- **Technician Assignment**: Assign repairs to specific technicians
- **Status Updates**: Real-time repair status management
- **Repair Tickets**: Thermal printer-compatible tickets with QR codes
- **Cost Tracking**: Estimated and final cost management
- **Parts Integration**: Track parts used in repairs

### 📦 Inventory Management
- **Stock Control**: Real-time quantity tracking with threshold alerts
- **Categories**: Organized inventory by categories (screens, batteries, etc.)
- **Cost Management**: Track both cost and selling price
- **Low Stock Alerts**: Automatic notifications when items fall below threshold
- **Parts Usage**: Integration with repair jobs for automatic stock updates
- **CRUD Operations**: Add, edit, and delete inventory items

### 💰 Invoice & Billing System
- **Automated Invoicing**: Generate invoices from completed repairs
- **Payment Tracking**: Multiple payment methods (cash, card, UPI, bank)
- **Invoice Management**: View, edit, and track all invoices
- **PDF Generation**: Professional invoice PDFs for printing
- **Payment Status**: Track paid amounts and outstanding balances
- **Financial Integration**: Automatic repair status updates when fully paid

### 📊 Reporting Dashboard
- **Financial Reports**: Revenue analysis with date range filtering
- **Repair Analytics**: Status distribution and completion metrics
- **Inventory Reports**: Stock levels and usage analysis
- **Customer Reports**: Customer activity and retention metrics
- **CSV Export**: Export all reports for external analysis
- **Visual Charts**: Interactive charts using Recharts library

### 🖨️ Thermal Printer Support
- **Repair Tickets**: Optimized for thermal printers (58mm/80mm)
- **QR Code Integration**: Each ticket includes QR code for tracking
- **Print Options**: Direct browser printing with optimized layouts
- **Company Branding**: Include company information from settings

### ⚙️ Settings Management
- **Company Information**: Name, address, contact details, GST number
- **Application Settings**: Currency, GST rate, notification preferences
- **Theme Support**: Light/dark mode toggle
- **Customization**: Personalize the application for your business

### 🔔 User Experience Features
- **Toast Notifications**: Real-time feedback for all user actions
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Pagination**: Consistent pagination across all data displays
- **Sorting**: Sortable columns for better data organization
- **Search Functionality**: Quick search across customers and repairs
- **Loading States**: Visual feedback during data operations

## 🛠️ Technology Stack

### Frontend
- **React 18.3.1**: Modern React with hooks and functional components
- **TypeScript**: Type-safe development with full TypeScript support
- **Vite 5.4.1**: Fast build tool and development server
- **React Router DOM**: Client-side routing for SPA navigation

### UI Framework
- **Tailwind CSS**: Utility-first CSS framework for styling
- **shadcn/ui**: High-quality, accessible React components
- **Radix UI**: Primitive components for complex UI elements
- **Lucide React**: Beautiful, customizable icons

### State Management
- **React Context API**: Global state management for app-wide data
- **TanStack React Query**: Server state management, caching, and synchronization
- **React Hook Form**: Performant forms with easy validation

### Database & Backend
- **Supabase**: PostgreSQL database with real-time capabilities
- **Supabase Client**: Type-safe database operations
- **Row Level Security**: Prepared for future authentication implementation

### Development Tools
- **ESLint**: Code linting with TypeScript support
- **Playwright**: End-to-end testing framework
- **PostCSS**: CSS processing with Autoprefixer
- **Lovable Tagger**: Development component tagging

### Deployment & Analytics
- **Vercel**: Serverless deployment platform
- **Vercel Analytics**: Performance and usage analytics
- **Vercel Speed Insights**: Core web vitals monitoring

### Key Libraries
- **date-fns**: Date manipulation and formatting
- **zod**: Schema validation for forms and data
- **jspdf & jspdf-autotable**: PDF generation for invoices
- **qrcode.react**: QR code generation for repair tickets
- **recharts**: Data visualization and charts
- **sonner**: Toast notification system
- **uuid**: Unique identifier generation

## 📋 Installation & Setup

### Prerequisites
- **Node.js**: Version 16 or higher
- **npm**: Package manager (comes with Node.js)
- **Supabase Account**: For database hosting
- **Git**: Version control system

### Step-by-Step Installation

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd gadget-repair-flow
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**

   Create a `.env` file in the root directory:
   ```env
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

   **Note**: The current version has hardcoded Supabase credentials for demo purposes. For production, use environment variables.

4. **Database Setup**

   The application uses Supabase with the following tables:
   - `customers`: Customer information
   - `repair_jobs`: Repair job tracking
   - `inventory_items`: Inventory management
   - `invoices`: Invoice data
   - `invoice_items`: Invoice line items
   - `parts_used`: Parts usage tracking
   - `settings`: Application settings

   Use the provided database schema scripts or the populate script:
   ```bash
   npm run populate-db
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:8080`

6. **Build for Production**
   ```bash
   npm run build
   ```

### Available Scripts
- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run build:dev`: Build in development mode
- `npm run preview`: Preview production build
- `npm run lint`: Run ESLint
- `npm run test`: Run Playwright tests
- `npm run test:ui`: Run tests with UI
- `npm run populate-db`: Populate database with sample data

## 📖 Usage Guide

### Getting Started
1. **Access the Dashboard**: Navigate to the main dashboard to see an overview of your repair shop
2. **Set Up Company Information**: Go to Settings to configure your company details
3. **Add Customers**: Start by adding customers to the system
4. **Create Repair Jobs**: Register new repair jobs for customers
5. **Manage Inventory**: Add inventory items and set stock thresholds
6. **Generate Invoices**: Create invoices for completed repairs
7. **Print Repair Tickets**: Use thermal printer support for repair tracking

### Customer Management Workflow
1. **Add New Customer**: Click "Add Customer" and fill in required information
2. **Search Customers**: Use the search bar to quickly find customers
3. **View Customer Details**: Click the eye icon to see customer history
4. **Edit Customer**: Update customer information as needed
5. **Delete Customer**: Remove customers who are no longer active

### Repair Job Workflow
1. **Create Repair Job**: Select customer, enter device details and issue description
2. **Assign Technician**: Assign the repair to a specific technician
3. **Update Status**: Move repair through lifecycle (Pending → In Progress → Completed → Delivered)
4. **Add Parts**: Track parts used during repair
5. **Print Repair Ticket**: Generate thermal printer ticket with QR code
6. **Complete Repair**: Mark as completed when work is finished
7. **Generate Invoice**: Create invoice for completed repair

### Inventory Management Workflow
1. **Add Inventory Items**: Enter item details, cost, price, and stock threshold
2. **Monitor Stock Levels**: Check dashboard for low stock alerts
3. **Update Quantities**: Adjust stock levels as needed
4. **Track Usage**: Monitor parts usage through repair jobs
5. **Manage Categories**: Organize items by categories

### Invoice & Payment Workflow
1. **Generate Invoice**: Create invoice from completed repair job
2. **Add Line Items**: Include labor, parts, and additional services
3. **Calculate Totals**: System automatically calculates subtotal, tax, and total
4. **Record Payments**: Track payments with multiple payment methods
5. **Print Invoice**: Generate PDF for customer
6. **Track Outstanding**: Monitor unpaid balances

### Reporting Workflow
1. **Access Reports**: Navigate to Reports section
2. **Select Report Type**: Choose from Financial, Repair, Inventory, or Customer reports
3. **Set Date Range**: Filter data by specific time periods
4. **View Analytics**: Analyze charts and metrics
5. **Export Data**: Download CSV files for external analysis

## 🗄️ Database Schema

### Core Tables

#### customers
- **Primary Key**: `id` (UUID)
- **Unique Constraint**: `phone` (must be unique)
- **Fields**: `name`, `email` (optional), `phone`, `address`, `created_at`
- **Relationships**: One-to-many with `repair_jobs` and `invoices`

#### repair_jobs
- **Primary Key**: `id` (UUID)
- **Foreign Keys**: `customer_id` → `customers.id`
- **Fields**: `brand`, `model`, `device_type`, `serial_number`, `issue_description`, `status`, `estimated_cost`, `final_cost`, `technician_name`, `notes`, `created_at`, `updated_at`, `completed_at`, `delivered_at`
- **Status Values**: `pending`, `inProgress`, `completed`, `delivered`, `cancelled`, `on_hold`

#### inventory_items
- **Primary Key**: `id` (UUID)
- **Fields**: `name`, `category`, `description`, `quantity`, `cost`, `price`, `threshold`, `created_at`
- **Relationships**: One-to-many with `parts_used`

#### invoices
- **Primary Key**: `id` (UUID)
- **Foreign Keys**: `customer_id` → `customers.id`, `repair_job_id` → `repair_jobs.id`
- **Fields**: `labor_cost`, `subtotal`, `tax`, `total`, `paid_amount`, `balance`, `payment_status`, `payment_method`, `due_date`, `created_at`
- **Payment Status**: `pending`, `partial`, `paid`

#### invoice_items
- **Primary Key**: `id` (UUID)
- **Foreign Keys**: `invoice_id` → `invoices.id`
- **Fields**: `description`, `quantity`, `unit_price`, `total`, `created_at`

#### parts_used
- **Primary Key**: `id` (UUID)
- **Foreign Keys**: `repair_job_id` → `repair_jobs.id`, `inventory_item_id` → `inventory_items.id`
- **Fields**: `quantity`, `cost`, `created_at`

#### settings
- **Primary Key**: `id` (UUID)
- **Fields**: `company_name`, `phone`, `email`, `address`, `gstin`, `logo`, `currency`, `gst_rate`, `dark_mode`, `sms_notifications`, `created_at`, `updated_at`

### Relationships
- **Customer → Repair Jobs**: One customer can have multiple repair jobs
- **Customer → Invoices**: One customer can have multiple invoices
- **Repair Job → Invoice**: One repair job can have one invoice
- **Repair Job → Parts Used**: One repair job can use multiple parts
- **Inventory Item → Parts Used**: One inventory item can be used in multiple repairs
- **Invoice → Invoice Items**: One invoice can have multiple line items

## 🔧 Development

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── Customer/       # Customer-related components
│   ├── Dashboard/      # Dashboard components
│   ├── Inventory/      # Inventory management components
│   ├── Invoice/        # Invoice and billing components
│   ├── Layout/         # Layout components (Header, Sidebar)
│   ├── Repair/         # Repair job components
│   ├── Reports/        # Reporting components
│   └── ui/             # shadcn/ui components
├── contexts/           # React Context providers
├── hooks/              # Custom React hooks
├── integrations/       # External service integrations
├── lib/                # Utility functions and helpers
├── pages/              # Page components
├── services/           # API service functions
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

### Custom Hooks
The application implements several reusable hooks for better code organization:

- **usePagination**: Handles pagination logic across components
- **useSorting**: Provides sorting functionality for data tables
- **useFilter**: Advanced filtering capabilities for data
- **useDebounce**: Debouncing for search and input operations
- **useLocalStorage**: Persistent local storage management
- **useMediaQuery**: Responsive design utilities
- **useExport**: Data export functionality
- **useForm**: Enhanced form handling
- **useToast**: Simplified toast notifications
- **useErrorLogger**: Error logging and monitoring

### Code Standards
- **TypeScript**: Strict typing throughout the application
- **ESLint**: Code linting with React and TypeScript rules
- **Component Organization**: Logical grouping by feature
- **Reusable Components**: DRY principle with shared components
- **Context API**: Global state management
- **Service Layer**: Separated API logic from components
- **Error Handling**: Comprehensive error logging and user feedback

### Development Best Practices
- **Component Composition**: Building complex UIs from simple components
- **Custom Hooks**: Extracting reusable logic into custom hooks
- **Type Safety**: Leveraging TypeScript for better development experience
- **Performance**: Using React Query for efficient data fetching and caching
- **Accessibility**: Following accessibility best practices with Radix UI
- **Responsive Design**: Mobile-first approach with Tailwind CSS

## 🚀 Deployment

### Vercel Deployment (Recommended)

The application is optimized for deployment on Vercel with the following configuration:

1. **Prerequisites**
   - Vercel account (free tier sufficient)
   - Git repository (GitHub, GitLab, or Bitbucket)

2. **Deployment Steps**
   - Connect your repository to Vercel
   - Configure build settings:
     - **Framework Preset**: Vite
     - **Build Command**: `npm run build`
     - **Output Directory**: `dist`
     - **Install Command**: `npm install`
   - Deploy automatically on git push

3. **Environment Variables** (Optional)
   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Vercel Configuration**
   The project includes `vercel.json` with optimized routing:
   ```json
   {
     "framework": "vite",
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "routes": [
       { "src": "/(.*)", "dest": "/index.html" }
     ]
   }
   ```

### Alternative Deployment Options
- **Netlify**: Compatible with static site deployment
- **GitHub Pages**: For public repositories
- **Firebase Hosting**: Google's hosting solution
- **AWS S3 + CloudFront**: Enterprise-grade hosting

### Production Considerations
- **Performance**: Enable compression and caching
- **Security**: Use environment variables for sensitive data
- **Monitoring**: Set up error tracking and analytics
- **Backup**: Regular database backups through Supabase

## 🧪 Testing

### Test Framework
The application uses **Playwright** for end-to-end testing, providing:
- Cross-browser testing (Chrome, Firefox, Safari)
- Mobile device simulation
- Visual regression testing
- API testing capabilities

### Running Tests
```bash
# Run all tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests in headed mode
npx playwright test --headed

# Run specific test file
npx playwright test tests/invoice.spec.ts
```

### Test Structure
```
tests/
├── invoice.spec.ts          # Invoice functionality tests
├── invoice-debug.spec.ts    # Debug tests for invoice issues
└── ...                      # Additional test files
```

### Test Coverage
Current test coverage includes:
- Invoice generation and PDF creation
- Customer management workflows
- Repair job lifecycle
- Basic navigation and UI interactions

### Future Testing Plans
- Unit tests for utility functions
- Component testing with React Testing Library
- API integration tests
- Performance testing
- Accessibility testing

## 🤝 Contributing

### Development Workflow
1. **Fork the Repository**: Create your own fork of the project
2. **Create Feature Branch**: `git checkout -b feature/your-feature-name`
3. **Make Changes**: Implement your feature or bug fix
4. **Test Changes**: Run tests and ensure everything works
5. **Commit Changes**: Use conventional commit messages
6. **Push Branch**: `git push origin feature/your-feature-name`
7. **Create Pull Request**: Submit PR with detailed description

### Code Standards
- **TypeScript**: All new code must be written in TypeScript
- **ESLint**: Follow the existing ESLint configuration
- **Prettier**: Code formatting is handled automatically
- **Component Structure**: Follow existing patterns for consistency
- **Testing**: Include tests for new features
- **Documentation**: Update documentation for significant changes

### Commit Message Format
```
type(scope): description

Examples:
feat(customer): add customer search functionality
fix(invoice): resolve PDF generation issue
docs(readme): update installation instructions
```

### Pull Request Guidelines
- **Clear Description**: Explain what changes were made and why
- **Screenshots**: Include screenshots for UI changes
- **Testing**: Describe how the changes were tested
- **Breaking Changes**: Clearly mark any breaking changes
- **Documentation**: Update relevant documentation

## 🗺️ Future Roadmap

### Version 2 (Planned)
- **Authentication System**: User login with role-based permissions
- **User Management**: Owner/Admin, Technician, and Front Desk roles
- **QR Code Scanning**: Enhanced QR code functionality with camera support
- **Advanced Reporting**: Detailed analytics and business insights
- **Multi-branch Support**: Support for multiple repair shop locations
- **Enhanced Error Tracking**: Improved monitoring and debugging tools
- **Code Quality Improvements**: Implement additional reusable hooks and patterns

### Version 3 (Future)
- **Customer Portal**: Online customer access to repair status
- **Mobile App**: Native mobile application for technicians
- **Advanced Inventory**: Supplier management and automated reordering
- **Marketing Tools**: SMS/Email campaigns and loyalty programs
- **API Integration**: Third-party service integrations
- **Offline Support**: Offline mode for mobile applications

### Planned Features
- **SMS Notifications**: Automated customer notifications
- **Appointment Scheduling**: Online booking system
- **Warranty Tracking**: Extended warranty management
- **Parts Ordering**: Direct supplier integration
- **Financial Integration**: Accounting software integration

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **shadcn/ui**: For the beautiful and accessible UI components
- **Supabase**: For providing an excellent backend-as-a-service platform
- **Vercel**: For seamless deployment and hosting
- **React Community**: For the amazing ecosystem and tools
- **Open Source Contributors**: For the libraries and tools that make this project possible

## 📞 Support

For support, questions, or feature requests:

- **Documentation**: Check the `docs/` folder for detailed documentation
- **Issues**: Create an issue on the GitHub repository
- **Discussions**: Use GitHub Discussions for general questions
- **Email**: Contact the development team for urgent matters

## 🔗 Links

- **Live Demo**: [https://gadget-repair-flow.vercel.app/](https://gadget-repair-flow.vercel.app/)
- **Documentation**: See `docs/` folder for detailed guides
- **Database Schema**: `docs/v1-database-schema.md`
- **User Guide**: `docs/v1-user-guide.md`
- **Deployment Guide**: `DEPLOYMENT.md`

---

**Version**: 1.0.0
**Last Updated**: January 2025
**Maintained by**: The Augster

This README serves as comprehensive documentation for the Mobile Phone Repair Shop Management System v1. For the most up-to-date information, please refer to the project repository.
