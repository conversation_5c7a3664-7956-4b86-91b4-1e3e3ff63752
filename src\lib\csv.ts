/**
 * Utility functions for CSV export
 */

/**
 * Convert an array of objects to CSV format
 * @param data Array of objects to convert
 * @param headers Optional custom headers (if not provided, will use object keys)
 * @param includeHeaders Whether to include headers in the output (default: true)
 * @returns CSV formatted string
 */
export function objectsToCSV(
  data: any[],
  headers?: { key: string; label: string }[],
  includeHeaders: boolean = true
): string {
  if (!data || !data.length) return '';

  // If headers not provided, generate from first object
  const csvHeaders = headers || Object.keys(data[0]).map(key => ({ key, label: key }));
  
  // Create header row if needed
  let csv = includeHeaders 
    ? csvHeaders.map(header => escapeCsvValue(header.label)).join(',') + '\n'
    : '';
  
  // Add data rows
  data.forEach(item => {
    const row = csvHeaders.map(header => {
      const value = item[header.key];
      return escapeCsvValue(formatCsvValue(value));
    }).join(',');
    csv += row + '\n';
  });
  
  return csv;
}

/**
 * Format a value for CSV export
 * @param value Value to format
 * @returns Formatted value as string
 */
function formatCsvValue(value: any): string {
  if (value === null || value === undefined) return '';
  
  // Format dates
  if (value instanceof Date) {
    return value.toLocaleDateString('en-IN');
  }
  
  // Format numbers
  if (typeof value === 'number') {
    return value.toString();
  }
  
  // Format booleans
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  
  // Format objects (convert to JSON string)
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  
  // Default: convert to string
  return String(value);
}

/**
 * Escape a value for CSV format
 * @param value Value to escape
 * @returns Escaped value
 */
function escapeCsvValue(value: string): string {
  // If value contains comma, newline or double quote, wrap in quotes
  if (/[",\n]/.test(value)) {
    // Replace double quotes with two double quotes
    value = value.replace(/"/g, '""');
    return `"${value}"`;
  }
  return value;
}

/**
 * Download data as a CSV file
 * @param csvContent CSV content
 * @param filename Filename for the download
 */
export function downloadCSV(csvContent: string, filename: string): void {
  // Add BOM for Excel compatibility
  const BOM = '\uFEFF';
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
  
  // Create download link
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
