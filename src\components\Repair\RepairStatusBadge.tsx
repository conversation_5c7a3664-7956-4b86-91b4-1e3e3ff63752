
import React from "react";
import { Badge } from "@/components/ui/badge";
import { RepairStatus } from "@/lib/data";
import { cn } from "@/lib/utils";

interface RepairStatusBadgeProps {
  status: RepairStatus;
}

const RepairStatusBadge: React.FC<RepairStatusBadgeProps> = ({ status }) => {
  const getStatusStyles = () => {
    switch (status) {
      case "pending":
        return "bg-amber-100 text-amber-800 hover:bg-amber-100";
      case "inProgress":
        return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      case "completed":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "delivered":
        return "bg-purple-100 text-purple-800 hover:bg-purple-100";
      case "cancelled":
        return "bg-red-100 text-red-800 hover:bg-red-100";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  const getStatusLabel = () => {
    switch (status) {
      case "inProgress":
        return "In Progress";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  return (
    <Badge className={cn(getStatusStyles(), "font-medium")}>
      {getStatusLabel()}
    </Badge>
  );
};

export default RepairStatusBadge;
