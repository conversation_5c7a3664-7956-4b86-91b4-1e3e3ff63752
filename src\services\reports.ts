import { supabase } from "@/integrations/supabase/client";
import { RepairStatus } from "@/lib/data";

export interface ReportFilters {
  dateFrom?: Date;
  dateTo?: Date;
  repairStatus?: RepairStatus[];
  deviceTypes?: string[];
  technicians?: string[];
  paymentMethods?: string[];
}

// Financial Reports
export async function getRevenueReport(filters: ReportFilters = {}) {
  const { dateFrom, dateTo } = filters;
  
  // Build query with date filters
  let query = supabase
    .from('invoices')
    .select('id, created_at, total, paid_amount, payment_status');
    
  if (dateFrom) {
    query = query.gte('created_at', dateFrom.toISOString());
  }
  
  if (dateTo) {
    query = query.lte('created_at', dateTo.toISOString());
  }
  
  const { data, error } = await query;
  
  if (error) {
    console.error("Error fetching revenue report:", error);
    throw error;
  }
  
  // Process data for reporting
  const dailyRevenue = {};
  const monthlyRevenue = {};
  
  data.forEach(invoice => {
    const date = new Date(invoice.created_at);
    const dayKey = date.toISOString().split('T')[0];
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    
    // Daily aggregation
    if (!dailyRevenue[dayKey]) {
      dailyRevenue[dayKey] = {
        total: 0,
        paid: 0,
        pending: 0
      };
    }
    
    dailyRevenue[dayKey].total += Number(invoice.total);
    dailyRevenue[dayKey].paid += Number(invoice.paid_amount);
    dailyRevenue[dayKey].pending += Number(invoice.total) - Number(invoice.paid_amount);
    
    // Monthly aggregation
    if (!monthlyRevenue[monthKey]) {
      monthlyRevenue[monthKey] = {
        total: 0,
        paid: 0,
        pending: 0
      };
    }
    
    monthlyRevenue[monthKey].total += Number(invoice.total);
    monthlyRevenue[monthKey].paid += Number(invoice.paid_amount);
    monthlyRevenue[monthKey].pending += Number(invoice.total) - Number(invoice.paid_amount);
  });
  
  return {
    dailyRevenue: Object.entries(dailyRevenue).map(([date, data]) => ({
      date,
      ...data
    })),
    monthlyRevenue: Object.entries(monthlyRevenue).map(([month, data]) => ({
      month,
      ...data
    })),
    summary: {
      totalRevenue: data.reduce((sum, invoice) => sum + Number(invoice.total), 0),
      totalPaid: data.reduce((sum, invoice) => sum + Number(invoice.paid_amount), 0),
      totalPending: data.reduce((sum, invoice) => sum + (Number(invoice.total) - Number(invoice.paid_amount)), 0),
      invoiceCount: data.length
    }
  };
}

// Repair Analytics
export async function getRepairAnalytics(filters: ReportFilters = {}) {
  const { dateFrom, dateTo, repairStatus, deviceTypes } = filters;
  
  // Build query with filters
  let query = supabase
    .from('repair_jobs')
    .select('*');
    
  if (dateFrom) {
    query = query.gte('created_at', dateFrom.toISOString());
  }
  
  if (dateTo) {
    query = query.lte('created_at', dateTo.toISOString());
  }
  
  if (repairStatus && repairStatus.length > 0) {
    query = query.in('status', repairStatus);
  }
  
  if (deviceTypes && deviceTypes.length > 0) {
    query = query.in('device_type', deviceTypes);
  }
  
  const { data, error } = await query;
  
  if (error) {
    console.error("Error fetching repair analytics:", error);
    throw error;
  }
  
  // Calculate repair times
  const repairTimes = data.map(repair => {
    const createdAt = new Date(repair.created_at);
    const completedAt = repair.completed_at ? new Date(repair.completed_at) : null;
    
    return {
      id: repair.id,
      deviceType: repair.device_type,
      brand: repair.brand,
      model: repair.model,
      status: repair.status,
      repairTime: completedAt ? Math.floor((completedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24)) : null // in days
    };
  });
  
  // Status distribution
  const statusCounts = data.reduce((counts, repair) => {
    counts[repair.status] = (counts[repair.status] || 0) + 1;
    return counts;
  }, {});
  
  // Device type distribution
  const deviceTypeCounts = data.reduce((counts, repair) => {
    counts[repair.device_type] = (counts[repair.device_type] || 0) + 1;
    return counts;
  }, {});
  
  // Brand distribution
  const brandCounts = data.reduce((counts, repair) => {
    counts[repair.brand] = (counts[repair.brand] || 0) + 1;
    return counts;
  }, {});
  
  return {
    repairTimes,
    statusDistribution: Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count
    })),
    deviceTypeDistribution: Object.entries(deviceTypeCounts).map(([deviceType, count]) => ({
      deviceType,
      count
    })),
    brandDistribution: Object.entries(brandCounts).map(([brand, count]) => ({
      brand,
      count
    })),
    averageRepairTime: repairTimes
      .filter(repair => repair.repairTime !== null)
      .reduce((sum, repair) => sum + repair.repairTime, 0) / 
      repairTimes.filter(repair => repair.repairTime !== null).length || 0
  };
}

// Inventory Reports
export async function getInventoryReport() {
  const { data, error } = await supabase
    .from('inventory_items')
    .select('*');
    
  if (error) {
    console.error("Error fetching inventory report:", error);
    throw error;
  }
  
  // Get parts used in repairs
  const { data: partsUsed, error: partsError } = await supabase
    .from('parts_used')
    .select('*');
    
  if (partsError) {
    console.error("Error fetching parts used:", partsError);
    throw partsError;
  }
  
  // Calculate usage statistics
  const usageStats = partsUsed.reduce((stats, part) => {
    if (!stats[part.inventory_item_id]) {
      stats[part.inventory_item_id] = {
        totalUsed: 0,
        usageCount: 0
      };
    }
    
    stats[part.inventory_item_id].totalUsed += part.quantity;
    stats[part.inventory_item_id].usageCount += 1;
    
    return stats;
  }, {});
  
  // Combine inventory data with usage stats
  const inventoryWithUsage = data.map(item => {
    const usage = usageStats[item.id] || { totalUsed: 0, usageCount: 0 };
    
    return {
      id: item.id,
      name: item.name,
      category: item.category,
      quantity: item.quantity,
      threshold: item.threshold,
      price: Number(item.price),
      cost: Number(item.cost),
      totalUsed: usage.totalUsed,
      usageCount: usage.usageCount,
      stockStatus: item.quantity <= item.threshold ? 'low' : 'normal',
      value: item.quantity * Number(item.cost)
    };
  });
  
  // Category summary
  const categorySummary = inventoryWithUsage.reduce((summary, item) => {
    if (!summary[item.category]) {
      summary[item.category] = {
        itemCount: 0,
        totalValue: 0,
        lowStockCount: 0
      };
    }
    
    summary[item.category].itemCount += 1;
    summary[item.category].totalValue += item.value;
    if (item.stockStatus === 'low') {
      summary[item.category].lowStockCount += 1;
    }
    
    return summary;
  }, {});
  
  return {
    inventoryItems: inventoryWithUsage,
    categorySummary: Object.entries(categorySummary).map(([category, data]) => ({
      category,
      ...data
    })),
    summary: {
      totalItems: data.length,
      totalValue: inventoryWithUsage.reduce((sum, item) => sum + item.value, 0),
      lowStockItems: inventoryWithUsage.filter(item => item.stockStatus === 'low').length
    }
  };
}

// Customer Reports
export async function getCustomerReport() {
  // Get customers
  const { data: customers, error: customerError } = await supabase
    .from('customers')
    .select('*');
    
  if (customerError) {
    console.error("Error fetching customers:", customerError);
    throw customerError;
  }
  
  // Get repairs by customer
  const { data: repairs, error: repairError } = await supabase
    .from('repair_jobs')
    .select('*');
    
  if (repairError) {
    console.error("Error fetching repairs:", repairError);
    throw repairError;
  }
  
  // Get invoices
  const { data: invoices, error: invoiceError } = await supabase
    .from('invoices')
    .select('*');
    
  if (invoiceError) {
    console.error("Error fetching invoices:", invoiceError);
    throw invoiceError;
  }
  
  // Calculate customer metrics
  const customerMetrics = customers.map(customer => {
    const customerRepairs = repairs.filter(repair => repair.customer_id === customer.id);
    const customerInvoices = invoices.filter(invoice => invoice.customer_id === customer.id);
    
    const totalSpent = customerInvoices.reduce((sum, invoice) => sum + Number(invoice.total), 0);
    const repairCount = customerRepairs.length;
    
    return {
      id: customer.id,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      createdAt: new Date(customer.created_at),
      repairCount,
      totalSpent,
      averageRepairValue: repairCount > 0 ? totalSpent / repairCount : 0,
      lastRepair: customerRepairs.length > 0 
        ? new Date(Math.max(...customerRepairs.map(r => new Date(r.created_at).getTime())))
        : null
    };
  });
  
  return {
    customers: customerMetrics,
    summary: {
      totalCustomers: customers.length,
      activeCustomers: customerMetrics.filter(c => c.repairCount > 0).length,
      averageRepairsPerCustomer: repairs.length / customers.length,
      averageSpendPerCustomer: customerMetrics.reduce((sum, c) => sum + c.totalSpent, 0) / customers.length
    },
    topCustomers: [...customerMetrics]
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 10)
  };
}
