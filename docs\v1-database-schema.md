# Mobile Repair Shop Management System - Database Schema

## Overview

This document describes the database schema for the Mobile Repair Shop Management System. The system uses Supabase, which is built on PostgreSQL, for data storage and retrieval.

## Tables

### customers

Stores information about customers who bring devices for repair.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| display_id | text | Human-readable ID (e.g., C1001) | NOT NULL |
| name | text | Customer's full name | NOT NULL |
| email | text | Customer's email address | NULL |
| phone | text | Customer's phone number | NOT NULL, UNIQUE |
| address | text | Customer's address | NULL |
| created_at | timestamptz | Record creation timestamp | NOT NULL, DEFAULT now() |
| updated_at | timestamptz | Record update timestamp | NOT NULL, DEFAULT now() |

**Indexes:**
- PRIMARY KEY (id)
- UNIQUE INDEX idx_customers_phone (phone)
- INDEX idx_customers_display_id (display_id)

### repair_jobs

Tracks repair jobs from creation to completion.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| customer_id | uuid | Reference to customers table | NOT NULL, FOREIGN KEY |
| device_type | text | Type of device (e.g., smartphone, tablet) | NOT NULL |
| brand | text | Device brand | NOT NULL |
| model | text | Device model | NOT NULL |
| serial_number | text | Device serial number | NULL |
| issue_description | text | Description of the issue | NOT NULL |
| technician_id | uuid | ID of assigned technician (for V2) | NULL |
| technician_name | text | Name of assigned technician (for V2) | NULL |
| status | text | Current status of repair | NOT NULL, CHECK (status IN ('pending', 'inProgress', 'completed', 'delivered', 'cancelled')) |
| estimated_cost | numeric | Estimated repair cost | NULL |
| final_cost | numeric | Final repair cost | NULL |
| notes | text[] | Array of notes about the repair | NULL |
| created_at | timestamptz | Record creation timestamp | NOT NULL, DEFAULT now() |
| updated_at | timestamptz | Record update timestamp | NOT NULL, DEFAULT now() |
| completed_at | timestamptz | When repair was completed | NULL |
| delivered_at | timestamptz | When device was delivered to customer | NULL |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (customer_id) REFERENCES customers(id)
- INDEX idx_repair_jobs_customer_id (customer_id)
- INDEX idx_repair_jobs_status (status)
- INDEX idx_repair_jobs_created_at (created_at)

### repair_parts

Tracks parts used in repairs.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| repair_id | uuid | Reference to repair_jobs table | NOT NULL, FOREIGN KEY |
| inventory_item_id | uuid | Reference to inventory_items table | NOT NULL, FOREIGN KEY |
| name | text | Name of the part | NOT NULL |
| quantity | integer | Quantity used | NOT NULL |
| unit_price | numeric | Price per unit | NOT NULL |
| total_price | numeric | Total price (quantity * unit_price) | NOT NULL |
| created_at | timestamptz | Record creation timestamp | NOT NULL, DEFAULT now() |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (repair_id) REFERENCES repair_jobs(id)
- FOREIGN KEY (inventory_item_id) REFERENCES inventory_items(id)
- INDEX idx_repair_parts_repair_id (repair_id)

### inventory_items

Stores information about parts and accessories in inventory.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| name | text | Item name | NOT NULL |
| category | text | Item category | NOT NULL |
| price | numeric | Selling price | NOT NULL |
| cost | numeric | Cost price | NOT NULL |
| quantity | integer | Current quantity in stock | NOT NULL, DEFAULT 0 |
| threshold | integer | Low stock threshold | NOT NULL, DEFAULT 5 |
| description | text | Item description | NULL |
| created_at | timestamptz | Record creation timestamp | NOT NULL, DEFAULT now() |
| updated_at | timestamptz | Record update timestamp | NOT NULL, DEFAULT now() |

**Indexes:**
- PRIMARY KEY (id)
- INDEX idx_inventory_items_category (category)
- INDEX idx_inventory_items_name (name)

### inventory_transactions

Tracks changes to inventory quantities.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| inventory_item_id | uuid | Reference to inventory_items table | NOT NULL, FOREIGN KEY |
| transaction_type | text | Type of transaction | NOT NULL, CHECK (transaction_type IN ('purchase', 'sale', 'adjustment', 'repair')) |
| quantity_change | integer | Change in quantity (positive or negative) | NOT NULL |
| reference_id | uuid | ID of related entity (repair, invoice, etc.) | NULL |
| reference_type | text | Type of reference ('repair', 'invoice', etc.) | NULL |
| notes | text | Transaction notes | NULL |
| created_at | timestamptz | Record creation timestamp | NOT NULL, DEFAULT now() |
| created_by | text | User who created the transaction | NULL |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (inventory_item_id) REFERENCES inventory_items(id)
- INDEX idx_inventory_transactions_item_id (inventory_item_id)
- INDEX idx_inventory_transactions_created_at (created_at)

### invoices

Stores invoice information for completed repairs.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| invoice_number | text | Human-readable invoice number | NOT NULL, UNIQUE |
| repair_job_id | uuid | Reference to repair_jobs table | NOT NULL, FOREIGN KEY |
| customer_id | uuid | Reference to customers table | NOT NULL, FOREIGN KEY |
| labor_cost | numeric | Cost of labor | NOT NULL, DEFAULT 0 |
| subtotal | numeric | Sum of all items before tax and discount | NOT NULL |
| discount_amount | numeric | Discount amount | NULL |
| discount_type | text | Type of discount ('percentage' or 'fixed') | NULL, CHECK (discount_type IN ('percentage', 'fixed')) |
| tax_rate | numeric | Tax rate percentage | NOT NULL |
| tax | numeric | Tax amount | NOT NULL |
| total | numeric | Total invoice amount | NOT NULL |
| paid_amount | numeric | Amount paid so far | NOT NULL, DEFAULT 0 |
| balance | numeric | Remaining balance | NOT NULL |
| payment_status | text | Payment status | NOT NULL, CHECK (payment_status IN ('paid', 'partial', 'pending', 'overdue')) |
| payment_method | text | Method used for initial payment | NULL, CHECK (payment_method IN ('cash', 'card', 'upi', 'bank')) |
| notes | text | Invoice notes | NULL |
| terms | text | Invoice terms and conditions | NULL |
| due_date | timestamptz | Payment due date | NULL |
| created_at | timestamptz | Record creation timestamp | NOT NULL, DEFAULT now() |
| created_by | text | User who created the invoice | NULL |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (repair_job_id) REFERENCES repair_jobs(id)
- FOREIGN KEY (customer_id) REFERENCES customers(id)
- UNIQUE INDEX idx_invoices_invoice_number (invoice_number)
- INDEX idx_invoices_repair_job_id (repair_job_id)
- INDEX idx_invoices_customer_id (customer_id)
- INDEX idx_invoices_payment_status (payment_status)
- INDEX idx_invoices_created_at (created_at)

### invoice_items

Stores line items for invoices.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| invoice_id | uuid | Reference to invoices table | NOT NULL, FOREIGN KEY |
| item_type | text | Type of item | NOT NULL, CHECK (item_type IN ('part', 'labor', 'other')) |
| name | text | Item name | NOT NULL |
| description | text | Item description | NULL |
| quantity | integer | Quantity | NOT NULL |
| unit_price | numeric | Price per unit | NOT NULL |
| total | numeric | Total price (quantity * unit_price) | NOT NULL |
| inventory_item_id | uuid | Reference to inventory_items table | NULL, FOREIGN KEY |
| created_at | timestamptz | Record creation timestamp | NOT NULL, DEFAULT now() |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (invoice_id) REFERENCES invoices(id)
- FOREIGN KEY (inventory_item_id) REFERENCES inventory_items(id)
- INDEX idx_invoice_items_invoice_id (invoice_id)

### payments

Tracks payments made against invoices.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| invoice_id | uuid | Reference to invoices table | NOT NULL, FOREIGN KEY |
| amount | numeric | Payment amount | NOT NULL |
| payment_method | text | Payment method | NOT NULL, CHECK (payment_method IN ('cash', 'card', 'upi', 'bank')) |
| payment_date | timestamptz | Date of payment | NOT NULL, DEFAULT now() |
| notes | text | Payment notes | NULL |
| created_at | timestamptz | Record creation timestamp | NOT NULL, DEFAULT now() |
| created_by | text | User who recorded the payment | NULL |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (invoice_id) REFERENCES invoices(id)
- INDEX idx_payments_invoice_id (invoice_id)
- INDEX idx_payments_payment_date (payment_date)

### settings

Stores application settings.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | uuid | Primary key | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| company | jsonb | Company settings | NOT NULL |
| application | jsonb | Application settings | NOT NULL |
| updated_at | timestamptz | Record update timestamp | NOT NULL, DEFAULT now() |

**Indexes:**
- PRIMARY KEY (id)

## Relationships

### One-to-Many Relationships

- **customers** → **repair_jobs**: One customer can have many repair jobs
- **customers** → **invoices**: One customer can have many invoices
- **repair_jobs** → **repair_parts**: One repair job can use many parts
- **repair_jobs** → **invoices**: One repair job can have one invoice (enforced in application logic)
- **inventory_items** → **repair_parts**: One inventory item can be used in many repairs
- **inventory_items** → **inventory_transactions**: One inventory item can have many transactions
- **invoices** → **invoice_items**: One invoice can have many line items
- **invoices** → **payments**: One invoice can have many payments

## Triggers and Functions

### update_inventory_on_repair_part

**Trigger**: AFTER INSERT ON repair_parts
**Function**: Decreases inventory quantity when a part is used in a repair

### update_inventory_on_transaction

**Trigger**: AFTER INSERT ON inventory_transactions
**Function**: Updates inventory quantity based on transaction

### update_invoice_status

**Trigger**: AFTER INSERT OR UPDATE ON payments
**Function**: Updates invoice payment status based on balance

### update_repair_status_on_invoice_payment

**Trigger**: AFTER UPDATE ON invoices
**Function**: Updates repair status to 'delivered' when invoice is fully paid

### update_timestamps

**Trigger**: BEFORE UPDATE ON multiple tables
**Function**: Updates the updated_at timestamp

## Row Level Security (RLS) Policies

Row Level Security policies will be implemented in Version 2 when user authentication is added. For Version 1, the database is accessed using the service role key with full access.

## Database Views

### active_repairs_view

Shows all repairs that are not in 'delivered' or 'cancelled' status.

```sql
CREATE VIEW active_repairs_view AS
SELECT r.*, c.name as customer_name, c.phone as customer_phone
FROM repair_jobs r
JOIN customers c ON r.customer_id = c.id
WHERE r.status NOT IN ('delivered', 'cancelled');
```

### low_stock_items_view

Shows inventory items that are below their threshold quantity.

```sql
CREATE VIEW low_stock_items_view AS
SELECT *
FROM inventory_items
WHERE quantity <= threshold;
```

### invoice_summary_view

Provides a summary of invoices with customer and repair information.

```sql
CREATE VIEW invoice_summary_view AS
SELECT i.*, c.name as customer_name, c.phone as customer_phone,
       r.device_type, r.brand, r.model, r.status as repair_status
FROM invoices i
JOIN customers c ON i.customer_id = c.id
JOIN repair_jobs r ON i.repair_job_id = r.id;
```

## Indexes

Indexes are created on frequently queried columns to improve performance:

- Customer phone numbers for quick lookup
- Repair status for filtering
- Invoice payment status for reporting
- Created_at timestamps for date range queries
- Foreign keys for relationship joins

## Data Types

- **uuid**: Used for all primary keys to ensure uniqueness across tables
- **text**: Used for string data like names, descriptions
- **numeric**: Used for monetary values to ensure precision
- **integer**: Used for quantities and counts
- **timestamptz**: Used for all timestamps to ensure timezone awareness
- **jsonb**: Used for structured data like settings
- **text[]**: Used for arrays of text like notes

## Constraints

- **NOT NULL**: Applied to required fields
- **UNIQUE**: Applied to fields that must be unique (phone numbers, invoice numbers)
- **CHECK**: Applied to fields with a limited set of valid values (status, payment_method)
- **FOREIGN KEY**: Applied to fields that reference other tables
- **DEFAULT**: Applied to provide default values (timestamps, initial quantities)

## Notes

- The database schema is designed to support the current Version 1 functionality
- Additional tables and columns will be added in future versions to support new features
- The schema includes some fields (like technician_id) that are not fully utilized in Version 1 but will be used in Version 2
