import { useState, useMemo } from 'react';

export type FilterConfig<T> = {
  [K in keyof T]?: {
    value: any;
    matcher?: (itemValue: any, filterValue: any) => boolean;
  };
};

export type FilterOperator = 'AND' | 'OR';

/**
 * A hook for filtering arrays of data with complex conditions
 * 
 * @param data The array of data to filter
 * @param initialFilters Initial filter configuration
 * @param operator Logical operator to combine filters (AND/OR)
 * @returns Filtered data and methods to update filters
 */
export function useFilter<T>(
  data: T[],
  initialFilters: FilterConfig<T> = {},
  operator: FilterOperator = 'AND'
) {
  const [filters, setFilters] = useState<FilterConfig<T>>(initialFilters);

  // Default matcher function for different data types
  const defaultMatcher = (itemValue: any, filterValue: any): boolean => {
    // Handle null/undefined values
    if (itemValue == null) return false;
    
    // Handle string values (case-insensitive contains)
    if (typeof itemValue === 'string' && typeof filterValue === 'string') {
      return itemValue.toLowerCase().includes(filterValue.toLowerCase());
    }
    
    // Handle array values (any element matches)
    if (Array.isArray(itemValue)) {
      return itemValue.some(v => defaultMatcher(v, filterValue));
    }
    
    // Handle date values
    if (itemValue instanceof Date && filterValue instanceof Date) {
      return itemValue.getTime() === filterValue.getTime();
    }
    
    // Handle date ranges
    if (itemValue instanceof Date && 
        filterValue && 
        typeof filterValue === 'object' && 
        (filterValue.from instanceof Date || filterValue.to instanceof Date)) {
      const time = itemValue.getTime();
      const fromTime = filterValue.from instanceof Date ? filterValue.from.getTime() : -Infinity;
      const toTime = filterValue.to instanceof Date ? filterValue.to.getTime() : Infinity;
      return time >= fromTime && time <= toTime;
    }
    
    // Handle numbers with ranges
    if (typeof itemValue === 'number' && 
        filterValue && 
        typeof filterValue === 'object' && 
        (typeof filterValue.min === 'number' || typeof filterValue.max === 'number')) {
      const min = typeof filterValue.min === 'number' ? filterValue.min : -Infinity;
      const max = typeof filterValue.max === 'number' ? filterValue.max : Infinity;
      return itemValue >= min && itemValue <= max;
    }
    
    // Handle boolean values
    if (typeof itemValue === 'boolean') {
      return itemValue === filterValue;
    }
    
    // Handle exact matches for other types
    return itemValue === filterValue;
  };

  // Apply filters to data
  const filteredData = useMemo(() => {
    const filterKeys = Object.keys(filters) as Array<keyof T>;
    
    if (filterKeys.length === 0) return data;
    
    return data.filter(item => {
      const results = filterKeys.map(key => {
        const filter = filters[key];
        if (!filter || filter.value == null) return true;
        
        const matcher = filter.matcher || defaultMatcher;
        return matcher(item[key], filter.value);
      });
      
      return operator === 'AND' 
        ? results.every(Boolean) 
        : results.some(Boolean);
    });
  }, [data, filters, operator]);

  // Set a single filter
  const setFilter = <K extends keyof T>(
    key: K, 
    value: any, 
    matcher?: (itemValue: any, filterValue: any) => boolean
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: { value, matcher }
    }));
  };

  // Remove a single filter
  const removeFilter = (key: keyof T) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({});
  };

  return {
    filters,
    filteredData,
    setFilter,
    removeFilter,
    resetFilters,
    setFilters
  };
}
