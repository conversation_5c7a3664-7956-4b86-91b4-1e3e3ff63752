
import { useState, useEffect } from "react";
import { useLocation, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import Header from "@/components/Layout/Header";
import Sidebar from "@/components/Layout/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { PlusCircle, Search, FileText, Eye, Edit, Phone, Calendar, Wrench, Smartphone, AlertCircle, Trash2, AlertTriangle, Loader2 } from "lucide-react";
import { formatINR } from "@/lib/currency";
import RepairStatusBadge from "@/components/Repair/RepairStatusBadge";
import RepairStatusUpdate from "@/components/Repair/RepairStatusUpdate";
import { useSorting } from "@/hooks/useSorting";
import { SortableHeader } from "@/components/ui/sortable-header";
import { getRepairJobs, getRepairJobById } from "@/services/repairs";
import { RepairJob, Invoice } from "@/lib/data";
import { toast } from "@/components/ui/sonner";
import { supabase } from "@/integrations/supabase/client";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import RepairForm from "@/components/Repair/RepairForm";
import RepairDetail from "@/components/Repair/RepairDetail";
import RepairTicketPrint from "@/components/Repair/RepairTicketPrint";
import InvoiceForm from "@/components/Invoice/InvoiceForm";
import InvoiceDetail from "@/components/Invoice/InvoiceDetail";
import { usePagination } from "@/hooks/usePagination";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAppContext } from "@/contexts/AppContext";

// Status filter options
const statusOptions = [
  { value: "all", label: "All Repairs" },
  { value: "pending", label: "Pending" },
  { value: "inProgress", label: "In Progress" },
  { value: "completed", label: "Completed" },
  { value: "delivered", label: "Delivered" },
  { value: "cancelled", label: "Cancelled" },
];

const Repairs = () => {
  const location = useLocation();
  const { id: repairIdParam } = useParams<{ id?: string }>();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [showReadyForInvoice, setShowReadyForInvoice] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedRepair, setSelectedRepair] = useState<RepairJob | null>(null);
  const [isInvoiceDialogOpen, setIsInvoiceDialogOpen] = useState(false);
  const [createdInvoice, setCreatedInvoice] = useState<Invoice | null>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [repairToUpdate, setRepairToUpdate] = useState<RepairJob | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [repairToEdit, setRepairToEdit] = useState<RepairJob | null>(null);
  const [showingPrintDialog, setShowingPrintDialog] = useState(false);
  const [newRepairData, setNewRepairData] = useState<RepairJob | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [repairToDelete, setRepairToDelete] = useState<RepairJob | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const { deleteRepairJob } = useAppContext();

  // Check if we should open the new repair dialog from the header button
  useEffect(() => {
    if (location.state && location.state.openNewRepairDialog) {
      setIsCreateDialogOpen(true);
      // Clear the state to prevent reopening on navigation
      window.history.replaceState({}, document.title);
    }
  }, [location]);

  const { data: repairJobs = [], isLoading, error, refetch } = useQuery<RepairJob[]>({
    queryKey: ['repairJobs'],
    queryFn: getRepairJobs,
  });

  useEffect(() => {
    if (error) {
      toast.error("Failed to load repair jobs", {
        description: "Please try again later or contact support."
      });
      console.error(error);
    }
  }, [error]);



  // Check if we need to open a specific repair based on URL parameter or location state
  useEffect(() => {
    if (repairIdParam) {
      handleViewRepair(repairIdParam);
    } else if (location.state && location.state.repairId) {
      handleViewRepair(location.state.repairId);
    }
  }, [location, repairIdParam, repairJobs]);

  // State to track which repairs have invoices
  const [repairsWithInvoices, setRepairsWithInvoices] = useState<Record<string, boolean>>({});

  // Check if a repair has an invoice
  const checkRepairHasInvoice = async (repairDbId: string): Promise<boolean> => {
    try {
      const { data } = await supabase
        .from('invoices')
        .select('id')
        .eq('repair_job_id', repairDbId)
        .limit(1);

      return data && data.length > 0;
    } catch (error) {
      console.error("Error checking if repair has invoice:", error);
      return false;
    }
  };

  // Check which repairs have invoices when the repair jobs list changes
  useEffect(() => {
    const checkInvoices = async () => {
      const invoiceStatus: Record<string, boolean> = {};

      // Only check for completed repairs
      const completedRepairs = repairJobs.filter(repair => repair.status === 'completed');

      for (const repair of completedRepairs) {
        const hasInvoice = await checkRepairHasInvoice(repair.dbId || repair.id);
        invoiceStatus[repair.id] = hasInvoice;
      }

      setRepairsWithInvoices(invoiceStatus);
    };

    if (repairJobs.length > 0) {
      checkInvoices();
    }
  }, [repairJobs]);

  // Apply filters (search, status, and ready for invoice)
  const filteredRepairs = repairJobs.filter((repair) => {
    // Status filter
    if (statusFilter !== "all" && repair.status !== statusFilter) {
      return false;
    }

    // Ready for Invoice filter
    if (showReadyForInvoice) {
      if (repair.status !== 'completed') {
        return false;
      }
      // Only show repairs without invoices
      if (repairsWithInvoices[repair.id]) {
        return false;
      }
    }

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      return (
        repair.id.toLowerCase().includes(query) ||
        repair.customerName.toLowerCase().includes(query) ||
        repair.brand.toLowerCase().includes(query) ||
        repair.model.toLowerCase().includes(query) ||
        repair.issueDescription.toLowerCase().includes(query) ||
        repair.status.toLowerCase().includes(query)
      );
    }

    return true;
  });

  // Define the type for repairs with virtual properties
  type RepairWithVirtual = RepairJob & { invoiceStatus: string };

  // First add virtual property for invoice status to each repair
  const repairsWithVirtualProps = filteredRepairs.map(repair => ({
    ...repair,
    // Virtual property for invoice status
    invoiceStatus: repair.status === 'completed' ?
      (repairsWithInvoices[repair.id] ? 'Invoiced' : 'Need Invoice') :
      ''
  }));

  // Then apply sorting to the enhanced repairs
  const {
    items: sortedRepairs,
    sortConfig,
    requestSort
  } = useSorting<RepairWithVirtual>(repairsWithVirtualProps, 'createdAt', 'desc');

  // Pagination
  const PAGE_SIZE = 10;
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedRepairs,
    goToPage,
    totalItems
  } = usePagination<RepairWithVirtual>({
    data: sortedRepairs,
    pageSize: PAGE_SIZE,
    initialPage: 1,
  });



  // Column definitions for the table
  const columns = [
    { key: 'id' as keyof RepairWithVirtual, label: 'ID' },
    { key: 'customerName' as keyof RepairWithVirtual, label: 'Customer' },
    { key: 'deviceType' as keyof RepairWithVirtual, label: 'Device' },
    { key: 'issueDescription' as keyof RepairWithVirtual, label: 'Issue' },
    { key: 'estimatedCost' as keyof RepairWithVirtual, label: 'Estimated Cost', className: 'text-right' },
    { key: 'status' as keyof RepairWithVirtual, label: 'Status', className: 'text-center' },
    { key: 'createdAt' as keyof RepairWithVirtual, label: 'Created', className: 'text-left' },
    { key: 'invoiceStatus' as keyof RepairWithVirtual, label: 'Invoice', className: 'text-center' },
  ];

  // Count repairs by status for the tabs
  const repairCounts = repairJobs.reduce((counts, repair) => {
    counts[repair.status] = (counts[repair.status] || 0) + 1;
    counts.all = (counts.all || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  const handleCreateSuccess = (repair?: RepairJob) => {
    setIsCreateDialogOpen(false);
    refetch();

    if (repair) {
      // If we have repair data, show success toast and print dialog
      toast.success("Repair job created successfully", {
        description: `Repair #${repair.id} for ${repair.customerName} has been created.`,
      });
      setNewRepairData(repair);
      setShowingPrintDialog(true);
    } else {
      toast.success("Repair job created successfully");
    }
  };

  const handlePrintDialogClosed = () => {
    setShowingPrintDialog(false);
    setNewRepairData(null);
  };

  // Function to handle status update success
  const handleStatusUpdateSuccess = () => {
    setIsStatusDialogOpen(false);
    refetch();
  };

  const handleInvoiceSuccess = (invoice: Invoice) => {
    console.log("handleInvoiceSuccess called with invoice:", invoice);
    setCreatedInvoice(invoice);
    setIsInvoiceDialogOpen(false);
  };

  const handleViewRepair = async (repairId: string) => {
    try {
      // First try to get the repair from the current data
      const existingRepair = repairJobs.find(r => r.id === repairId);

      if (existingRepair) {
        setSelectedRepair(existingRepair);
        setIsDetailDialogOpen(true);
        return;
      }

      // If not found in current data, try to get it from the API
      const repair = await getRepairJobById(repairId);
      if (repair) {
        setSelectedRepair(repair);
        setIsDetailDialogOpen(true);
      } else {
        toast.error("Repair job not found");
      }
    } catch (error) {
      console.error("Error fetching repair job:", error);
      toast.error("Failed to load repair job details");
    }
  };

  const handleDeleteClick = (repair: RepairJob) => {
    setRepairToDelete(repair);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteRepair = async () => {
    if (!repairToDelete) return;

    try {
      setIsDeleting(true);
      // Use the database ID for deletion
      await deleteRepairJob(repairToDelete.dbId || repairToDelete.id);
      setIsDeleteDialogOpen(false);
      setRepairToDelete(null);
      refetch(); // Refresh the repair jobs list
    } catch (error) {
      console.error("Error deleting repair job:", error);
      // Error is already handled by the context with toast
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setRepairToDelete(null);
  };

  const handleGenerateInvoice = async (repairId: string) => {
    console.log("[DEBUG] Generating invoice for repair ID:", repairId);
    try {
      // Find the repair in the current data
      const existingRepair = repairJobs.find(r => r.id === repairId);

      if (!existingRepair) {
        // If not found in current data, try to get it from the API
        console.log("Fetching repair from API...");
        const repair = await getRepairJobById(repairId);
        if (!repair) {
          console.error("Repair job not found in API");
          toast.error("Repair job not found");
          return;
        }
        console.log("Found repair from API:", repair);
        setSelectedRepair(repair);
      } else {
        console.log("[DEBUG] Found repair in current data:", existingRepair);
        console.log("[DEBUG] Repair ID:", existingRepair.id);
        console.log("[DEBUG] Repair DB ID:", existingRepair.dbId);
        setSelectedRepair(existingRepair);
      }

      // Check if an invoice already exists for this repair job
      const repairDbId = existingRepair?.dbId || existingRepair?.id;
      const { data, error } = await supabase
        .from('invoices')
        .select('id, invoice_number')
        .eq('repair_job_id', repairDbId);

      if (error) {
        console.error("Error checking for existing invoices:", error);
        toast.error("Failed to check for existing invoices");
        return;
      }

      // Safely cast the data to the expected type with a double assertion
      const existingInvoices = (data as unknown) as Array<{ id: string; invoice_number?: string }> || [];

      if (existingInvoices.length > 0) {
        // Show a confirmation dialog
        const invoiceIdentifier = existingInvoices[0].invoice_number || existingInvoices[0].id || 'unknown';
        if (confirm(`An invoice (${invoiceIdentifier}) already exists for this repair job. Do you want to create another one?`)) {
          setIsInvoiceDialogOpen(true);
        } else {
          // User chose not to create another invoice
          toast.info("Invoice creation cancelled");
        }
      } else {
        // No existing invoice, proceed normally
        setIsInvoiceDialogOpen(true);
      }
    } catch (error) {
      console.error("Error fetching repair job:", error);
      toast.error("Failed to load repair job details");
    }
  };

  return (
    <div className="flex min-h-screen">
      <Sidebar isOpen={sidebarOpen} toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? "md:ml-64" : "md:ml-16"}`}>
        <Header toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isOpen={sidebarOpen} />

        <main className="px-4 py-6 md:px-6">
          <div className="flex flex-col gap-4 mb-6">
            {/* Status Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-blue-800">In Progress</span>
                    <span className="text-2xl font-bold text-blue-900">
                      {repairCounts['inProgress'] || 0}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-amber-50 border-amber-200">
                <CardContent className="p-4">
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-amber-800">Completed</span>
                    <span className="text-2xl font-bold text-amber-900">
                      {repairCounts['completed'] || 0}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-green-50 border-green-200">
                <CardContent className="p-4">
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-green-800">Delivered</span>
                    <span className="text-2xl font-bold text-green-900">
                      {repairCounts['delivered'] || 0}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-amber-50 border-amber-200">
                <CardContent className="p-4">
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-amber-800">Need Invoice</span>
                    <span className="text-2xl font-bold text-amber-900">
                      {Object.entries(repairsWithInvoices).filter(([_, hasInvoice]) => !hasInvoice).length}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <h1 className="text-2xl font-bold">Repair Jobs</h1>

              <div className="flex flex-col gap-2 md:flex-row md:items-center">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search repairs..."
                    className="w-full md:w-[300px] pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  New Repair
                </Button>
              </div>
            </div>

            {/* Status filter tabs */}
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              <Tabs defaultValue="all" onValueChange={setStatusFilter} value={statusFilter}>
                <TabsList className="flex flex-wrap h-auto p-1">
                  {statusOptions.map(option => (
                    <TabsTrigger
                      key={option.value}
                      value={option.value}
                      className="px-3 py-1.5 text-xs sm:text-sm"
                    >
                      {option.label}
                      {repairCounts[option.value] !== undefined && (
                        <span className="ml-1.5 rounded-full bg-primary/10 px-1.5 py-0.5 text-xs">
                          {repairCounts[option.value] || 0}
                        </span>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>

              <div className="flex items-center space-x-2 bg-muted/50 p-2 rounded-md border">
                <Checkbox
                  id="ready-for-invoice"
                  checked={showReadyForInvoice}
                  onCheckedChange={(checked) => setShowReadyForInvoice(checked === true)}
                />
                <Label
                  htmlFor="ready-for-invoice"
                  className="text-sm font-medium flex items-center cursor-pointer"
                >
                  <AlertCircle className="h-4 w-4 mr-1 text-amber-500" />
                  Show Completed Repairs Without Invoices
                </Label>
              </div>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>All Repairs</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <p>Loading repair jobs...</p>
                </div>
              ) : (
                <>
                  {/* Desktop view - Table */}
                  <div className="hidden md:block overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          {columns.map(column => (
                            <SortableHeader
                              key={String(column.key)}
                              column={column}
                              sortConfig={sortConfig}
                              onSort={requestSort}
                            />
                          ))}
                          <th className="py-3 px-4 text-center">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {paginatedRepairs.length === 0 ? (
                          <tr>
                            <td colSpan={9} className="text-center py-8">
                              {searchQuery.trim() || statusFilter !== "all" ?
                                "No repairs match your filters." :
                                "No repair jobs found."}
                            </td>
                          </tr>
                        ) : (
                          paginatedRepairs.map(repair => (
                          <tr key={repair.id} className="border-b hover:bg-muted/50">
                            <td className="py-3 px-4">{repair.id}</td>
                            <td className="py-3 px-4 font-medium">{repair.customerName}</td>
                            <td className="py-3 px-4">
                              {repair.brand} {repair.model}
                            </td>
                            <td className="py-3 px-4">
                              {repair.issueDescription.length > 30
                                ? `${repair.issueDescription.substring(0, 30)}...`
                                : repair.issueDescription}
                            </td>
                            <td className="py-3 px-4 text-right">
                              {repair.estimatedCost ? formatINR(repair.estimatedCost) : '-'}
                            </td>
                            <td className="py-3 px-4 text-center">
                              <RepairStatusBadge status={repair.status} />
                            </td>
                            <td className="py-3 px-4">
                              {repair.createdAt.toLocaleDateString('en-IN')}
                            </td>
                            <td className="py-3 px-4 text-center">
                              {repair.status === 'completed' ? (
                                repairsWithInvoices[repair.id] ? (
                                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                    Invoiced
                                  </Badge>
                                ) : (
                                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                                    Need Invoice
                                  </Badge>
                                )
                              ) : null}
                            </td>
                            <td className="py-3 px-4 text-center">
                              <div className="flex justify-center space-x-2">
                                <div className="flex gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() => handleViewRepair(repair.id)}
                                  >
                                    <Eye className="h-4 w-4" />
                                    <span className="sr-only">View</span>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() => {
                                      console.log("Setting repair to update:", repair);
                                      setRepairToUpdate(repair);
                                      setIsStatusDialogOpen(true);
                                    }}
                                  >
                                    <Edit className="h-4 w-4" />
                                    <span className="sr-only">Edit Status</span>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                    onClick={() => handleDeleteClick(repair)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    <span className="sr-only">Delete</span>
                                  </Button>
                                </div>
                                {/* Make sure the button is visible for completed repairs */}
                                {(repair.status === 'completed') && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-1"
                                    onClick={() => handleGenerateInvoice(repair.id)}
                                  >
                                    <FileText className="h-4 w-4" />
                                    <span>Invoice</span>
                                  </Button>
                                )}

                              </div>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                  </div>

                  {/* Mobile view - Cards */}
                  <div className="md:hidden space-y-4">
                    {paginatedRepairs.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        {searchQuery.trim() || statusFilter !== "all" ?
                          "No repairs match your filters." :
                          "No repair jobs found."}
                      </div>
                    ) : (
                      paginatedRepairs.map(repair => (
                        <div key={repair.id} className="border rounded-lg p-4 hover:bg-muted/50">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h3 className="font-medium">{repair.brand} {repair.model}</h3>
                              <p className="text-xs text-muted-foreground">
                                ID: {repair.id}
                              </p>
                            </div>
                            <div className="flex flex-col gap-1 items-end">
                              <RepairStatusBadge status={repair.status} />
                              {repair.status === 'completed' ? (
                                repairsWithInvoices[repair.id] ? (
                                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs">
                                    Invoiced
                                  </Badge>
                                ) : (
                                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 text-xs">
                                    Need Invoice
                                  </Badge>
                                )
                              ) : null}
                            </div>
                          </div>

                          <div className="grid grid-cols-1 gap-1 text-sm mb-3">
                            <div className="flex items-center">
                              <Smartphone className="h-3 w-3 mr-2 text-muted-foreground" />
                              <span className="truncate">{repair.deviceType}</span>
                            </div>
                            <div className="flex items-center">
                              <Wrench className="h-3 w-3 mr-2 text-muted-foreground" />
                              <span className="truncate">{repair.issueDescription.substring(0, 40)}{repair.issueDescription.length > 40 ? '...' : ''}</span>
                            </div>
                            <div className="flex items-center">
                              <Phone className="h-3 w-3 mr-2 text-muted-foreground" />
                              <span>{repair.customerName}</span>
                            </div>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-2 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                Created: {repair.createdAt.toLocaleDateString('en-IN')}
                              </span>
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium">
                              {repair.estimatedCost ? formatINR(repair.estimatedCost) : 'No estimate'}
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewRepair(repair.id)}
                              >
                                View
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  console.log("Setting repair to update:", repair);
                                  setRepairToUpdate(repair);
                                  setIsStatusDialogOpen(true);
                                }}
                              >
                                Update
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-destructive hover:text-destructive"
                                onClick={() => handleDeleteClick(repair)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                              {repair.status === 'completed' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleGenerateInvoice(repair.id)}
                                >
                                  Invoice
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4">
                      <p className="text-sm text-muted-foreground">
                        Showing {Math.min((currentPage - 1) * PAGE_SIZE + 1, totalItems)} to {Math.min(currentPage * PAGE_SIZE, totalItems)} of {totalItems} repairs
                      </p>

                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                if (currentPage > 1) goToPage(currentPage - 1);
                              }}
                              aria-disabled={currentPage === 1}
                              className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                            />
                          </PaginationItem>

                          {Array.from({ length: totalPages }).map((_, i) => {
                            const page = i + 1;
                            // Show first page, last page, and pages around current page
                            if (
                              page === 1 ||
                              page === totalPages ||
                              (page >= currentPage - 1 && page <= currentPage + 1)
                            ) {
                              return (
                                <PaginationItem key={page}>
                                  <PaginationLink
                                    href="#"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      goToPage(page);
                                    }}
                                    isActive={page === currentPage}
                                  >
                                    {page}
                                  </PaginationLink>
                                </PaginationItem>
                              );
                            }

                            // Show ellipsis
                            if (
                              (page === 2 && currentPage > 3) ||
                              (page === totalPages - 1 && currentPage < totalPages - 2)
                            ) {
                              return (
                                <PaginationItem key={page}>
                                  <PaginationEllipsis />
                                </PaginationItem>
                              );
                            }

                            return null;
                          })}

                          <PaginationItem>
                            <PaginationNext
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                if (currentPage < totalPages) goToPage(currentPage + 1);
                              }}
                              aria-disabled={currentPage === totalPages}
                              className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </main>
      </div>

      {/* Create Repair Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Create New Repair Job</DialogTitle>
            <DialogDescription>Enter the details to create a new repair job</DialogDescription>
          </DialogHeader>
          <div className="overflow-y-auto pr-1 -mr-1 flex-1">
            <RepairForm onSuccess={handleCreateSuccess} />
          </div>
        </DialogContent>
      </Dialog>

      {/* Generate Invoice Dialog */}
      <Dialog open={isInvoiceDialogOpen} onOpenChange={setIsInvoiceDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Generate Invoice</DialogTitle>
            <DialogDescription>Create an invoice for this repair job</DialogDescription>
          </DialogHeader>
          <div className="overflow-y-auto pr-1 -mr-1 flex-1">
            {selectedRepair && (
              <InvoiceForm repairJob={selectedRepair} onSuccess={handleInvoiceSuccess} />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Invoice Preview Dialog */}
      <Dialog
        open={createdInvoice !== null}
        onOpenChange={(open) => !open && setCreatedInvoice(null)}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Invoice Preview</DialogTitle>
            <DialogDescription>Review the generated invoice details</DialogDescription>
          </DialogHeader>
          <div className="overflow-y-auto pr-1 -mr-1 flex-1">
            {createdInvoice && <InvoiceDetail invoice={createdInvoice} />}
          </div>
        </DialogContent>
      </Dialog>

      {/* Update Status Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent className="max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Update Repair Status</DialogTitle>
            <DialogDescription>Change the current status of this repair job</DialogDescription>
          </DialogHeader>
          <div className="overflow-y-auto pr-1 -mr-1 flex-1">
            {repairToUpdate && (
              <RepairStatusUpdate
                repair={repairToUpdate}
                onSuccess={handleStatusUpdateSuccess}
                onCancel={() => setIsStatusDialogOpen(false)}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Repair Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Repair Details</DialogTitle>
            <DialogDescription>View detailed information about this repair job</DialogDescription>
          </DialogHeader>
          <div className="overflow-y-auto pr-1 -mr-1">
            {selectedRepair && (
              <RepairDetail
                repair={selectedRepair}
                onClose={() => setIsDetailDialogOpen(false)}
                onGenerateInvoice={handleGenerateInvoice}
                onUpdateStatus={(repair) => {
                  setRepairToUpdate(repair);
                  setIsDetailDialogOpen(false);
                  setIsStatusDialogOpen(true);
                }}
                onEdit={(repair) => {
                  setRepairToEdit(repair);
                  setIsDetailDialogOpen(false);
                  setIsEditDialogOpen(true);
                }}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Repair Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Edit Repair</DialogTitle>
            <DialogDescription>Modify the details of this repair job</DialogDescription>
          </DialogHeader>
          <div className="overflow-y-auto pr-1 -mr-1 flex-1">
            {repairToEdit && (
              <RepairForm
                repair={repairToEdit}
                onSuccess={() => {
                  setIsEditDialogOpen(false);
                  refetch();
                }}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Print Ticket Dialog */}
      {showingPrintDialog && newRepairData && (
        <Dialog open={showingPrintDialog} onOpenChange={setShowingPrintDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Print Repair Ticket</DialogTitle>
              <DialogDescription>
                Would you like to print a repair ticket for this repair job?
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <p className="text-sm text-muted-foreground mb-4">
                Printing a repair ticket allows you to attach it to the device for easy identification throughout the repair process.
              </p>

              <div className="flex justify-center">
                <RepairTicketPrint
                  repair={newRepairData}
                  buttonText="Print Repair Ticket"
                  buttonVariant="default"
                  buttonSize="default"
                  className="w-full"
                  onPrintSuccess={() => {
                    toast.success("Repair ticket printed successfully");
                    handlePrintDialogClosed();
                  }}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => {
                toast.info("Printing skipped");
                handlePrintDialogClosed();
              }}>
                Skip Printing
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Repair Job</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete repair job {repairToDelete?.id} for {repairToDelete?.customerName}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center gap-2 p-4 bg-amber-50 border border-amber-200 rounded-md">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            <p className="text-sm text-amber-700">
              Repair jobs with existing invoices cannot be deleted. You must delete the invoices first.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelDelete}>Cancel</Button>
            <Button
              variant="destructive"
              onClick={handleDeleteRepair}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Repair Job'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Repairs;
