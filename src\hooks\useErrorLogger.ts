import { useCallback } from 'react';
import { logError as logErrorUtil, ErrorSeverity, ErrorContext } from '@/utils/errorLogger';

/**
 * Hook for logging errors within components
 * @param componentName The name of the component using this hook
 * @returns Object with methods for logging errors at different severity levels
 */
export function useErrorLogger(componentName: string) {
  // Ensure component name is not too long
  const safeComponentName = componentName?.substring(0, 50) || 'UnknownComponent';

  const logInfo = useCallback(
    (message: string, context: Omit<ErrorContext, 'component'> = {}) => {
      try {
        logErrorUtil(message, ErrorSeverity.INFO, { ...context, component: safeComponentName });
      } catch (e) {
        // Silently fail if error logging fails
      }
    },
    [safeComponentName]
  );

  const logWarning = useCallback(
    (message: string, context: Omit<ErrorContext, 'component'> = {}) => {
      try {
        logErrorUtil(message, ErrorSeverity.WARNING, { ...context, component: safeComponentName });
      } catch (e) {
        // Silently fail if error logging fails
      }
    },
    [safeComponentName]
  );

  const logErrorFn = useCallback(
    (error: Error | string, context: Omit<ErrorContext, 'component'> = {}) => {
      try {
        logErrorUtil(error, ErrorSeverity.ERROR, { ...context, component: safeComponentName });
      } catch (e) {
        // Silently fail if error logging fails
      }
    },
    [safeComponentName]
  );

  const logCritical = useCallback(
    (error: Error | string, context: Omit<ErrorContext, 'component'> = {}) => {
      try {
        logErrorUtil(error, ErrorSeverity.CRITICAL, { ...context, component: safeComponentName });
      } catch (e) {
        // Silently fail if error logging fails
      }
    },
    [safeComponentName]
  );

  /**
   * Wrap an async function with error logging
   * @param fn The async function to wrap
   * @param actionName Name of the action for context
   * @returns A wrapped function that logs errors
   */
  const withErrorLogging = useCallback(
    <T extends any[], R>(
      fn: (...args: T) => Promise<R>,
      actionName: string
    ) => {
      return async (...args: T): Promise<R> => {
        try {
          return await fn(...args);
        } catch (error) {
          try {
            // Limit action name length
            const safeActionName = actionName?.substring(0, 50) || 'unknownAction';
            logErrorFn(
              error instanceof Error ? error : new Error(String(error)),
              { action: safeActionName }
            );
          } catch (e) {
            // Silently fail if error logging fails
          }
          throw error;
        }
      };
    },
    [safeComponentName, logErrorFn]
  );

  return {
    logInfo,
    logWarning,
    logError: logErrorFn,
    logCritical,
    withErrorLogging,
  };
}
