
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Legend, Tooltip } from "recharts";

interface RepairStatusChartProps {
  statusCounts: {
    pending: number;
    inProgress: number;
    completed: number;
    delivered: number;
    cancelled: number;
    on_hold: number;
  };
  isLoading: boolean;
}

const RepairStatusChart: React.FC<RepairStatusChartProps> = ({ statusCounts, isLoading }) => {
  const data = [
    { name: "Pending", value: statusCounts.pending, color: "#FFA500" },
    { name: "In Progress", value: statusCounts.inProgress, color: "#3B82F6" },
    { name: "Completed", value: statusCounts.completed, color: "#10B981" },
    { name: "Delivered", value: statusCounts.delivered, color: "#8B5CF6" },
    { name: "Cancelled", value: statusCounts.cancelled, color: "#EF4444" },
    { name: "On Hold", value: statusCounts.on_hold, color: "#9CA3AF" }
  ].filter(item => item.value > 0);

  const RADIAN = Math.PI / 180;
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (isLoading) {
    return (
      <Card className="col-span-1 md:col-span-2">
        <CardHeader className="pb-2">
          <CardTitle>Repair Status Distribution</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center">
          <div style={{ width: '100%', height: 300 }} className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.length === 0) {
    return (
      <Card className="col-span-1 md:col-span-2">
        <CardHeader className="pb-2">
          <CardTitle>Repair Status Distribution</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center">
          <div style={{ width: '100%', height: 300 }} className="flex items-center justify-center">
            <p className="text-muted-foreground">No repair data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="col-span-1 md:col-span-2">
      <CardHeader className="pb-2">
        <CardTitle>Repair Status Distribution</CardTitle>
      </CardHeader>
      <CardContent className="flex justify-center">
        <div style={{ width: '100%', height: 300 }}>
          <ResponsiveContainer>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) => [`${value} repairs`, '']}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default RepairStatusChart;
