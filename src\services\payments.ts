import { supabase } from "@/integrations/supabase/client";
import type { Payment } from "@/lib/data";
import { v4 as uuidv4 } from 'uuid';

/**
 * Get all payments for an invoice
 * @param invoiceId The invoice ID
 * @returns Array of payments
 */
export async function getPaymentsByInvoiceId(invoiceId: string): Promise<Payment[]> {
  const { data, error } = await supabase
    .from('payments')
    .select('*')
    .eq('invoice_id', invoiceId)
    .order('payment_date', { ascending: false });
    
  if (error) {
    console.error("Error fetching payments:", error);
    throw error;
  }
  
  return data.map(payment => ({
    id: payment.id,
    invoiceId: payment.invoice_id,
    amount: Number(payment.amount),
    paymentMethod: payment.payment_method as 'cash' | 'card' | 'upi' | 'bank',
    paymentDate: new Date(payment.payment_date),
    notes: payment.notes,
    receiptNumber: payment.receipt_number
  }));
}

/**
 * Create a new payment receipt
 * @param payment The payment data
 * @returns The created payment with receipt number
 */
export async function createPaymentReceipt(payment: Payment): Promise<string> {
  // Generate a receipt number
  const year = new Date().getFullYear();
  const randomNum = Math.floor(1000 + Math.random() * 9000);
  const receiptNumber = `REC-${year}-${randomNum}`;
  
  return receiptNumber;
}

/**
 * Get payment by ID
 * @param id The payment ID
 * @returns The payment or null if not found
 */
export async function getPaymentById(id: string): Promise<Payment | null> {
  const { data, error } = await supabase
    .from('payments')
    .select('*')
    .eq('id', id)
    .single();
    
  if (error) {
    console.error("Error fetching payment:", error);
    return null;
  }
  
  return {
    id: data.id,
    invoiceId: data.invoice_id,
    amount: Number(data.amount),
    paymentMethod: data.payment_method as 'cash' | 'card' | 'upi' | 'bank',
    paymentDate: new Date(data.payment_date),
    notes: data.notes,
    receiptNumber: data.receipt_number
  };
}

/**
 * Delete a payment
 * @param id The payment ID
 * @returns True if successful, false otherwise
 */
export async function deletePayment(id: string): Promise<boolean> {
  const { error } = await supabase
    .from('payments')
    .delete()
    .eq('id', id);
    
  if (error) {
    console.error("Error deleting payment:", error);
    return false;
  }
  
  return true;
}
