import React, { useState, useEffect } from "react";
import { Customer, RepairJob, Invoice } from "@/lib/data";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { CalendarClock, Phone, Mail, MapPin, Loader2, Trash2, AlertTriangle } from "lucide-react";
import { useAppContext } from "@/contexts/AppContext";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { getRepairJobsByCustomerId } from "@/services/repairs";
import { getInvoices } from "@/services/invoices";
import { formatINR } from "@/lib/currency";
import { useNavigate } from "react-router-dom";
import { usePagination } from "@/hooks/usePagination";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

// Map of repair status to badge color
const statusColors = {
  pending: "bg-yellow-500",
  inProgress: "bg-blue-500",
  completed: "bg-green-500",
  delivered: "bg-purple-500",
  cancelled: "bg-red-500"
};

// Map of repair status to display text
const statusText = {
  pending: "Pending",
  inProgress: "In Progress",
  completed: "Completed",
  delivered: "Delivered",
  cancelled: "Cancelled"
};

interface CustomerDetailProps {
  customer: Customer;
  onClose: () => void;
}

const CustomerDetail: React.FC<CustomerDetailProps> = ({ customer, onClose }) => {
  const [repairs, setRepairs] = useState<RepairJob[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingInvoices, setIsLoadingInvoices] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [invoiceError, setInvoiceError] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const navigate = useNavigate();
  const { deleteCustomer } = useAppContext();

  // Fetch repairs for this customer
  useEffect(() => {
    const fetchRepairs = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const customerRepairs = await getRepairJobsByCustomerId(customer.id);
        setRepairs(customerRepairs || []);
      } catch (err) {
        console.error("Error fetching repairs:", err);
        setError("Failed to load repair history. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchRepairs();
  }, [customer.id]);

  // Fetch invoices for this customer
  useEffect(() => {
    const fetchInvoices = async () => {
      try {
        setIsLoadingInvoices(true);
        setInvoiceError(null);
        const customerInvoices = await getInvoices({ customerId: customer.id });
        setInvoices(customerInvoices || []);
      } catch (err) {
        console.error("Error fetching invoices:", err);
        setInvoiceError("Failed to load invoice history. Please try again.");
      } finally {
        setIsLoadingInvoices(false);
      }
    };

    fetchInvoices();
  }, [customer.id]);

  // Constants
  const REPAIRS_PER_PAGE = 5;
  const INVOICES_PER_PAGE = 5;

  // Pagination for all repairs
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedRepairs,
    goToPage
  } = usePagination({
    data: repairs,
    pageSize: REPAIRS_PER_PAGE,
    initialPage: 1,
  });

  // Pagination for all invoices
  const {
    currentPage: invoicesCurrentPage,
    totalPages: invoicesTotalPages,
    paginatedData: paginatedInvoices,
    goToPage: goToInvoicesPage
  } = usePagination({
    data: invoices,
    pageSize: INVOICES_PER_PAGE,
    initialPage: 1,
  });

  // Group repairs by status
  const repairsByStatus = repairs.reduce((acc, repair) => {
    if (!acc[repair.status]) {
      acc[repair.status] = [];
    }
    acc[repair.status].push(repair);
    return acc;
  }, {} as Record<string, RepairJob[]>);

  // Create state for status tab pagination
  const [statusPagination, setStatusPagination] = useState<Record<string, {
    currentPage: number;
    paginatedData: RepairJob[];
    totalPages: number;
  }>>({});

  // Initialize pagination for each status tab
  useEffect(() => {
    // Create a new pagination state object
    const newStatusPagination: Record<string, any> = {};

    // For each status group, create a pagination state
    Object.entries(repairsByStatus).forEach(([status, statusRepairs]) => {
      // Calculate pagination values
      const totalPages = Math.max(1, Math.ceil(statusRepairs.length / REPAIRS_PER_PAGE));
      const currentPage = 1;
      const paginatedData = statusRepairs.slice(0, REPAIRS_PER_PAGE);

      // Create pagination state for this status
      newStatusPagination[status] = {
        currentPage,
        totalPages,
        paginatedData,
        // Function to change page for this status tab
        goToPage: (page: number) => {
          setStatusPagination(prev => ({
            ...prev,
            [status]: {
              ...prev[status],
              currentPage: page,
              paginatedData: statusRepairs.slice(
                (page - 1) * REPAIRS_PER_PAGE,
                page * REPAIRS_PER_PAGE
              )
            }
          }));
        }
      };
    });

    // Update the state with all pagination data
    setStatusPagination(newStatusPagination);
  }, [repairs, REPAIRS_PER_PAGE]); // Remove repairsByStatus from dependencies

  // Calculate statistics
  const totalRepairs = repairs.length;
  const activeRepairs = repairs.filter(r => r.status === "pending" || r.status === "inProgress").length;
  const completedRepairs = repairs.filter(r => r.status === "completed" || r.status === "delivered").length;

  // Calculate total spent
  const totalSpent = repairs
    .filter(r => r.finalCost && (r.status === "completed" || r.status === "delivered"))
    .reduce((sum, repair) => sum + (repair.finalCost || 0), 0);

  // Handle navigation to repair details
  const handleViewRepair = (repairId: string) => {
    // Navigate to the repairs page with the specific repair ID
    navigate(`/repairs/${repairId}`, { state: { repairId } });
    onClose(); // Close the customer detail dialog
  };

  // Handle navigation to invoice details
  const handleViewInvoice = (invoiceId: string) => {
    // Navigate to the invoices page with the specific invoice ID
    navigate(`/invoices/${invoiceId}`, { state: { invoiceId } });
    onClose(); // Close the customer detail dialog
  };

  // Handle customer deletion
  const handleDeleteCustomer = async () => {
    try {
      setIsDeleting(true);
      await deleteCustomer(customer.id);
      onClose(); // Close the customer detail dialog
    } catch (error) {
      console.error("Error deleting customer:", error);
      // Error is already handled by the context with toast
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  return (
    <div className="space-y-6 pb-2">
      {/* Customer Information */}
      <div>
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold">{customer.name}</h2>
            <p className="text-muted-foreground">Customer ID: {customer.displayId || customer.id.substring(0, 8)}</p>
            <p className="text-muted-foreground">
              <CalendarClock className="inline-block mr-1 h-4 w-4" />
              Customer since {customer.createdAt.toLocaleDateString()}
            </p>
          </div>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setIsDeleteDialogOpen(true)}
            className="flex items-center gap-1"
          >
            <Trash2 className="h-4 w-4" />
            Delete
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div className="space-y-2">
            <div className="flex items-center">
              <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">{customer.phone}</span>
            </div>
            {customer.email ? (
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 flex-shrink-0" />
                <span className="truncate">{customer.email}</span>
              </div>
            ) : null}
            {customer.address && (
              <div className="flex items-start">
                <MapPin className="h-4 w-4 mr-2 mt-1 flex-shrink-0" />
                <span className="break-words">{customer.address}</span>
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-2">
            <Card className="h-auto">
              <CardContent className="p-3 sm:pt-6">
                <div className="text-xl sm:text-2xl font-bold">{totalRepairs}</div>
                <p className="text-xs text-muted-foreground">Total Repairs</p>
              </CardContent>
            </Card>
            <Card className="h-auto">
              <CardContent className="p-3 sm:pt-6">
                <div className="text-xl sm:text-2xl font-bold">{activeRepairs}</div>
                <p className="text-xs text-muted-foreground">Active Repairs</p>
              </CardContent>
            </Card>
            <Card className="h-auto">
              <CardContent className="p-3 sm:pt-6">
                <div className="text-xl sm:text-2xl font-bold">{completedRepairs}</div>
                <p className="text-xs text-muted-foreground">Completed</p>
              </CardContent>
            </Card>
            <Card className="h-auto">
              <CardContent className="p-3 sm:pt-6">
                <div className="text-xl sm:text-2xl font-bold truncate">{formatINR(totalSpent)}</div>
                <p className="text-xs text-muted-foreground">Total Spent</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Customer</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {customer.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center gap-2 p-4 bg-amber-50 border border-amber-200 rounded-md">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            <p className="text-sm text-amber-700">
              Customers with existing repair jobs or invoices cannot be deleted. You must delete those records first.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCustomer}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Customer'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Separator />

      {/* Repair History */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Repair History</h3>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-500">{error}</div>
        ) : repairs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No repair history found for this customer.
          </div>
        ) : (
          <Tabs defaultValue="repairs">
            <TabsList className="mb-4 flex flex-wrap gap-1">
              <TabsTrigger value="repairs">Repairs ({repairs.length})</TabsTrigger>
              <TabsTrigger value="invoices">Invoices ({invoices.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="repairs">
              <div className="space-y-0">
                {paginatedRepairs.map(repair => (
                  <RepairCard
                    key={repair.id}
                    repair={repair}
                    onViewClick={() => handleViewRepair(repair.id)}
                  />
                ))}

                {totalPages > 1 && (
                  <div className="mt-4 flex justify-center">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              if (currentPage > 1) goToPage(currentPage - 1);
                            }}
                            aria-disabled={currentPage === 1}
                            className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                          />
                        </PaginationItem>

                        {Array.from({ length: totalPages }).map((_, i) => {
                          const page = i + 1;
                          // Show first page, last page, and pages around current page
                          if (
                            page === 1 ||
                            page === totalPages ||
                            (page >= currentPage - 1 && page <= currentPage + 1)
                          ) {
                            return (
                              <PaginationItem key={page}>
                                <PaginationLink
                                  href="#"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    goToPage(page);
                                  }}
                                  isActive={page === currentPage}
                                >
                                  {page}
                                </PaginationLink>
                              </PaginationItem>
                            );
                          }

                          // Show ellipsis
                          if (
                            (page === 2 && currentPage > 3) ||
                            (page === totalPages - 1 && currentPage < totalPages - 2)
                          ) {
                            return (
                              <PaginationItem key={page}>
                                <PaginationEllipsis />
                              </PaginationItem>
                            );
                          }

                          return null;
                        })}

                        <PaginationItem>
                          <PaginationNext
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              if (currentPage < totalPages) goToPage(currentPage + 1);
                            }}
                            aria-disabled={currentPage === totalPages}
                            className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="invoices">
              <div className="space-y-0">
                {isLoadingInvoices ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : invoiceError ? (
                  <div className="text-center py-8 text-red-500">{invoiceError}</div>
                ) : invoices.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No invoices found for this customer.
                  </div>
                ) : (
                  <>
                    {paginatedInvoices.map(invoice => (
                      <InvoiceCard
                        key={invoice.id}
                        invoice={invoice}
                        onViewClick={() => handleViewInvoice(invoice.id)}
                      />
                    ))}

                    {invoicesTotalPages > 1 && (
                      <div className="mt-4 flex justify-center">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (invoicesCurrentPage > 1) goToInvoicesPage(invoicesCurrentPage - 1);
                                }}
                                aria-disabled={invoicesCurrentPage === 1}
                                className={invoicesCurrentPage === 1 ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>

                            {Array.from({ length: invoicesTotalPages }).map((_, i) => {
                              const page = i + 1;
                              // Show first page, last page, and pages around current page
                              if (
                                page === 1 ||
                                page === invoicesTotalPages ||
                                (page >= invoicesCurrentPage - 1 && page <= invoicesCurrentPage + 1)
                              ) {
                                return (
                                  <PaginationItem key={page}>
                                    <PaginationLink
                                      href="#"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        goToInvoicesPage(page);
                                      }}
                                      isActive={page === invoicesCurrentPage}
                                    >
                                      {page}
                                    </PaginationLink>
                                  </PaginationItem>
                                );
                              }

                              // Show ellipsis
                              if (
                                (page === 2 && invoicesCurrentPage > 3) ||
                                (page === invoicesTotalPages - 1 && invoicesCurrentPage < invoicesTotalPages - 2)
                              ) {
                                return (
                                  <PaginationItem key={page}>
                                    <PaginationEllipsis />
                                  </PaginationItem>
                                );
                              }

                              return null;
                            })}

                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (invoicesCurrentPage < invoicesTotalPages) goToInvoicesPage(invoicesCurrentPage + 1);
                                }}
                                aria-disabled={invoicesCurrentPage === invoicesTotalPages}
                                className={invoicesCurrentPage === invoicesTotalPages ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )}
              </div>
            </TabsContent>

            {Object.entries(repairsByStatus).map(([status, statusRepairs]) => {
              // Get pagination data for this status or use defaults
              const statusPaginationData = statusPagination[status] || {
                currentPage: 1,
                totalPages: Math.ceil(statusRepairs.length / REPAIRS_PER_PAGE),
                paginatedData: statusRepairs.slice(0, REPAIRS_PER_PAGE),
                goToPage: () => {}
              };

              // Extract pagination values
              const statusCurrentPage = statusPaginationData.currentPage;
              const statusTotalPages = statusPaginationData.totalPages;
              const statusPaginatedRepairs = statusPaginationData.paginatedData;
              // Use type assertion to handle the optional goToPage function
              const statusGoToPage = (statusPaginationData as any).goToPage || (() => {});

              return (
                <TabsContent key={status} value={status}>
                  <div className="space-y-0">
                    {statusPaginatedRepairs?.map(repair => (
                      <RepairCard
                        key={repair.id}
                        repair={repair}
                        onViewClick={() => handleViewRepair(repair.id)}
                      />
                    ))}

                    {statusTotalPages > 1 && (
                      <div className="mt-4 flex justify-center">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (statusCurrentPage > 1) statusGoToPage(statusCurrentPage - 1);
                                }}
                                aria-disabled={statusCurrentPage === 1}
                                className={statusCurrentPage === 1 ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>

                            {Array.from({ length: statusTotalPages }).map((_, i) => {
                              const page = i + 1;
                              if (
                                page === 1 ||
                                page === statusTotalPages ||
                                (page >= statusCurrentPage - 1 && page <= statusCurrentPage + 1)
                              ) {
                                return (
                                  <PaginationItem key={page}>
                                    <PaginationLink
                                      href="#"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        statusGoToPage(page);
                                      }}
                                      isActive={page === statusCurrentPage}
                                    >
                                      {page}
                                    </PaginationLink>
                                  </PaginationItem>
                                );
                              }

                              if (
                                (page === 2 && statusCurrentPage > 3) ||
                                (page === statusTotalPages - 1 && statusCurrentPage < statusTotalPages - 2)
                              ) {
                                return (
                                  <PaginationItem key={page}>
                                    <PaginationEllipsis />
                                  </PaginationItem>
                                );
                              }

                              return null;
                            })}

                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (statusCurrentPage < statusTotalPages) statusGoToPage(statusCurrentPage + 1);
                                }}
                                aria-disabled={statusCurrentPage === statusTotalPages}
                                className={statusCurrentPage === statusTotalPages ? "pointer-events-none opacity-50" : ""}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </div>
                </TabsContent>
              );
            })}
          </Tabs>
        )}
      </div>
    </div>
  );
};

// Invoice Card Component
interface InvoiceCardProps {
  invoice: Invoice;
  onViewClick: () => void;
}

const InvoiceCard: React.FC<InvoiceCardProps> = ({ invoice, onViewClick }) => {
  // Map payment status to badge color
  const statusColors = {
    paid: "bg-green-500",
    partial: "bg-yellow-500",
    pending: "bg-blue-500",
    overdue: "bg-red-500"
  };

  // Map payment status to display text
  const statusText = {
    paid: "Paid",
    partial: "Partial",
    pending: "Pending",
    overdue: "Overdue"
  };

  return (
    <Card className="mb-4 last:mb-0">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-base">
              Invoice #{invoice.invoiceNumber}
            </CardTitle>
            <CardDescription>
              Repair ID: {invoice.repairJobId} • {new Date(invoice.createdAt).toLocaleDateString()}
            </CardDescription>
          </div>
          <Badge className={statusColors[invoice.paymentStatus]}>
            {statusText[invoice.paymentStatus]}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center text-sm mb-2">
          <div>
            <span className="font-medium">Total: {formatINR(invoice.total)}</span>
          </div>
          <div>
            {invoice.balance > 0 ? (
              <span className="text-muted-foreground">Balance: {formatINR(invoice.balance)}</span>
            ) : (
              <span className="text-green-600 font-medium">Fully Paid</span>
            )}
          </div>
        </div>
        <div className="mt-4 flex justify-end">
          <Button
            size="sm"
            onClick={onViewClick}
            variant="outline"
          >
            View Invoice
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Repair Card Component
interface RepairCardProps {
  repair: RepairJob;
  onViewClick: () => void;
}

const RepairCard: React.FC<RepairCardProps> = ({ repair, onViewClick }) => {
  return (
    <Card className="mb-4 last:mb-0">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-base">
              {repair.brand} {repair.model}
            </CardTitle>
            <CardDescription>
              {repair.deviceType} • ID: {repair.id} • {repair.status === 'completed' ? 'Completed' : repair.status === 'inProgress' ? 'In Progress' : repair.status}
            </CardDescription>
          </div>
          <Badge className={statusColors[repair.status]}>
            {statusText[repair.status]}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-sm mb-2">{repair.issueDescription}</div>
        <div className="flex justify-between items-center text-sm">
          <div className="text-muted-foreground">
            {repair.createdAt.toLocaleDateString()}
          </div>
          <div>
            {repair.finalCost ? (
              <span className="font-medium">{formatINR(repair.finalCost)}</span>
            ) : repair.estimatedCost ? (
              <span className="text-muted-foreground">Est: {formatINR(repair.estimatedCost)}</span>
            ) : null}
          </div>
        </div>
        <div className="mt-4 flex justify-end">
          <Button
            size="sm"
            onClick={onViewClick}
            variant="outline"
          >
            View Details
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomerDetail;
