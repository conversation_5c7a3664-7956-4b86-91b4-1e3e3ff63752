# Mobile Repair Shop Management System - User Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Dashboard](#dashboard)
4. [Managing Customers](#managing-customers)
5. [Managing Repairs](#managing-repairs)
6. [Managing Inventory](#managing-inventory)
7. [Creating and Managing Invoices](#creating-and-managing-invoices)
8. [Generating Reports](#generating-reports)
9. [System Settings](#system-settings)
10. [Printing](#printing)
11. [Tips and Best Practices](#tips-and-best-practices)

## Introduction

Welcome to the Mobile Repair Shop Management System! This user guide will help you navigate and use the system effectively to manage your repair shop operations. The system is designed to streamline customer management, repair tracking, inventory control, invoicing, and reporting.

## Getting Started

### Accessing the System

1. Open your web browser and navigate to the application URL
2. The system will load the dashboard, which is the main landing page

### Navigation

- **Sidebar**: Use the sidebar on the left to navigate between different sections of the application
- **Header**: The top bar contains search functionality and access to quick actions
- **Breadcrumbs**: Help you understand your current location in the application

## Dashboard

The dashboard provides a quick overview of your repair shop's operations.

### Key Components

- **Repair Status Summary**: Shows the count of repairs in different statuses
- **Revenue Summary**: Displays financial metrics for different time periods
- **Inventory Alerts**: Highlights items that are low in stock
- **Recent Repairs**: Shows the most recently created or updated repair jobs
- **Repair Status Chart**: Visual representation of repair status distribution

### Quick Actions

- **New Repair**: Click the "New Repair" tab at the top of the dashboard to quickly create a new repair job without navigating away

## Managing Customers

The Customers section allows you to manage all your customer information.

### Viewing Customers

1. Click on "Customers" in the sidebar
2. The main view displays a list of all customers with basic information
3. Use the search box to find specific customers by name, phone, or email
4. Click on column headers to sort the list

### Adding a New Customer

1. Click the "Add Customer" button in the top right
2. Fill in the required information:
   - Name (required)
   - Phone Number (required and must be unique)
   - Email (optional)
   - Address (optional)
3. Click "Save" to create the customer

### Viewing Customer Details

1. Find the customer in the list
2. Click the "View" (eye icon) button in the Actions column
3. A dialog will open showing detailed customer information and repair history
4. Use the tabs to filter repairs by status

### Editing a Customer

1. Find the customer in the list
2. Click the "Edit" (pencil icon) button in the Actions column
3. Update the information as needed
4. Click "Save" to update the customer

### Deleting a Customer

1. Find the customer in the list
2. Click the "Delete" button in the Actions column
3. Confirm the deletion in the confirmation dialog

## Managing Repairs

The Repairs section is the core of the system, allowing you to track all repair jobs.

### Viewing Repairs

1. Click on "Repairs" in the sidebar
2. The main view displays a list of all repair jobs with key information
3. Use the search box to find specific repairs
4. Use the status filter to show only repairs in a specific status
5. Click on column headers to sort the list

### Creating a New Repair

1. Click the "New Repair" button in the top right
2. Select a customer:
   - Search for an existing customer by name or phone
   - Or create a new customer by clicking "Add New Customer"
3. Enter device information:
   - Device Type
   - Brand
   - Model
   - Serial Number (optional)
4. Enter the issue description
5. Enter an estimated cost (optional)
6. Click "Save" to create the repair job

### Viewing Repair Details

1. Find the repair in the list
2. Click the "View" (eye icon) button in the Actions column
3. A dialog will open showing detailed repair information
4. From here, you can:
   - Update the repair status
   - Generate an invoice (for completed repairs)
   - Print a repair ticket
   - Edit the repair details

### Updating Repair Status

1. Find the repair in the list
2. Click the "Update" button in the Actions column
3. Select the new status from the dropdown
4. Add notes if needed
5. Click "Update" to save the changes

### Printing a Repair Ticket

1. Open the repair details
2. Click the "Print Ticket" button
3. A new window will open with the printable ticket
4. Use your browser's print function to send it to your printer

## Managing Inventory

The Inventory section helps you track parts and accessories.

### Viewing Inventory

1. Click on "Inventory" in the sidebar
2. The main view displays a list of all inventory items
3. Use the search box to find specific items
4. Use the category filter to show only items in a specific category
5. Items below their threshold quantity are highlighted

### Adding a New Inventory Item

1. Click the "Add Item" button in the top right
2. Fill in the required information:
   - Name
   - Category
   - Selling Price
   - Cost Price
   - Quantity
   - Low Stock Threshold
   - Description (optional)
3. Click "Save" to add the item

### Editing an Inventory Item

1. Find the item in the list
2. Click the "Edit" (pencil icon) button in the Actions column
3. Update the information as needed
4. Click "Save" to update the item

### Adjusting Quantity

1. Find the item in the list
2. Click the "Adjust" button in the Actions column
3. Enter the new quantity or the amount to add/subtract
4. Add a reason for the adjustment
5. Click "Save" to update the quantity

## Creating and Managing Invoices

The Invoices section allows you to generate bills and track payments.

### Viewing Invoices

1. Click on "Invoices" in the sidebar
2. The main view displays a list of all invoices
3. Use the search box to find specific invoices
4. Use the status filter to show only invoices in a specific payment status
5. Click on column headers to sort the list

### Creating a New Invoice

1. From the Repairs page, find a completed repair
2. Click the "Invoice" button in the Actions column
3. The invoice form will open with repair details pre-filled
4. Add line items:
   - Parts (select from inventory)
   - Labor charges
   - Other charges
5. Apply discounts if needed
6. Enter payment information:
   - Payment method
   - Amount paid
7. Add notes or terms if needed
8. Click "Save" to create the invoice

### Viewing Invoice Details

1. Find the invoice in the list
2. Click the "View" (eye icon) button in the Actions column
3. A dialog will open showing detailed invoice information
4. From here, you can:
   - Record additional payments
   - Print the invoice
   - View payment history

### Recording a Payment

1. Open the invoice details
2. Click the "Add Payment" button
3. Enter the payment amount
4. Select the payment method
5. Add notes if needed
6. Click "Save" to record the payment

### Printing an Invoice

1. Open the invoice details
2. Click the "Print" button
3. A new window will open with the printable invoice
4. Use your browser's print function to send it to your printer

## Generating Reports

The Reports section provides business analytics and insights.

### Accessing Reports

1. Click on "Reports" in the sidebar
2. Select the type of report you want to view:
   - Financial Reports
   - Repair Reports
   - Inventory Reports
   - Customer Reports

### Filtering Reports

1. Use the date range selector to specify the period for the report
2. Apply additional filters specific to each report type
3. Click "Apply Filters" to update the report

### Exporting Reports

1. View the desired report
2. Click the "Export to CSV" button
3. The report data will be downloaded as a CSV file

## System Settings

The Settings section allows you to customize the system.

### Accessing Settings

1. Click on "Settings" in the sidebar
2. The settings page has multiple sections:
   - Company Information
   - Application Settings
   - Appearance

### Updating Company Information

1. Navigate to the Company Information section
2. Update the fields:
   - Company Name
   - Phone
   - Email
   - Address
   - GST Number
   - Logo (upload)
3. Click "Save" to update the information

### Changing Application Settings

1. Navigate to the Application Settings section
2. Update the fields:
   - Currency
   - GST Rate
   - Notification Preferences
3. Click "Save" to update the settings

## Printing

The system supports printing various documents.

### Repair Tickets

1. Open a repair's details
2. Click the "Print Ticket" button
3. A new window will open with the printable ticket
4. Use your browser's print function to send it to your printer

### Invoices

1. Open an invoice's details
2. Click the "Print" button
3. A new window will open with the printable invoice
4. Use your browser's print function to send it to your printer

### Reports

1. Generate the desired report
2. Use the browser's print function to print the current view
3. Alternatively, export to CSV and print from another application

## Tips and Best Practices

### General Tips

- **Regular Backups**: Ensure your database is backed up regularly
- **Update Inventory**: Keep your inventory quantities accurate
- **Complete Information**: Fill in as much information as possible for customers and repairs
- **Status Updates**: Keep repair statuses up to date for accurate reporting
- **Search Function**: Use the search function to quickly find records

### Workflow Recommendations

1. **New Customer Visit**:
   - Register the customer
   - Create a repair job
   - Print a repair ticket
   - Update the status to "In Progress" when work begins

2. **Completing a Repair**:
   - Update parts used from inventory
   - Change status to "Completed"
   - Generate an invoice
   - Record payment (full or partial)
   - Change status to "Delivered" when the customer collects the device

3. **End of Day**:
   - Check for any pending status updates
   - Review low stock alerts
   - Check for overdue invoices

### Keyboard Shortcuts

- **Search**: Press `/` to focus the search box
- **Navigation**: Use `Alt + 1-5` to quickly navigate between main sections
- **Save Forms**: Press `Ctrl + Enter` to save forms

---

This user guide covers the basic functionality of the Mobile Repair Shop Management System. For technical details or advanced features, please refer to the system documentation.
