import { useState, useCallback, ChangeEvent, FormEvent } from 'react';

type ValidationRule<T> = {
  validate: (value: any, formValues: T) => boolean;
  message: string;
};

type FieldConfig<T> = {
  initialValue: any;
  required?: boolean;
  requiredMessage?: string;
  validations?: ValidationRule<T>[];
};

type FormConfig<T> = {
  [K in keyof T]: FieldConfig<T>;
};

type FormErrors<T> = {
  [K in keyof T]?: string;
};

/**
 * A hook for managing form state, validation, and submission
 * 
 * @param config Configuration for form fields and validation
 * @param onSubmit Function to call when form is submitted and valid
 * @returns Form state, handlers, and utilities
 */
export function useForm<T extends Record<string, any>>(
  config: FormConfig<T>,
  onSubmit: (values: T) => void | Promise<void>
) {
  // Initialize form values from config
  const initialValues = Object.entries(config).reduce((acc, [key, fieldConfig]) => {
    acc[key as keyof T] = fieldConfig.initialValue;
    return acc;
  }, {} as T);

  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<FormErrors<T>>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(false);

  // Validate a single field
  const validateField = useCallback((name: keyof T, value: any): string | undefined => {
    const fieldConfig = config[name];
    
    // Check if required
    if (fieldConfig.required && (value === undefined || value === null || value === '')) {
      return fieldConfig.requiredMessage || 'This field is required';
    }
    
    // Run custom validations
    if (fieldConfig.validations) {
      for (const rule of fieldConfig.validations) {
        if (!rule.validate(value, values)) {
          return rule.message;
        }
      }
    }
    
    return undefined;
  }, [config, values]);

  // Validate all fields
  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrors<T> = {};
    let formIsValid = true;
    
    Object.keys(config).forEach(key => {
      const fieldName = key as keyof T;
      const error = validateField(fieldName, values[fieldName]);
      
      if (error) {
        newErrors[fieldName] = error;
        formIsValid = false;
      }
    });
    
    setErrors(newErrors);
    setIsValid(formIsValid);
    
    return formIsValid;
  }, [config, validateField, values]);

  // Handle field change
  const handleChange = useCallback((
    e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    
    // Handle different input types
    let newValue: any = value;
    
    if (type === 'checkbox') {
      newValue = (e.target as HTMLInputElement).checked;
    } else if (type === 'number') {
      newValue = value === '' ? '' : Number(value);
    }
    
    setValues(prev => ({
      ...prev,
      [name]: newValue
    }));
    
    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
    
    // Validate field
    const error = validateField(name as keyof T, newValue);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
    
    // Update form validity
    validateForm();
  }, [validateField, validateForm]);

  // Set a field value programmatically
  const setFieldValue = useCallback((name: keyof T, value: any) => {
    setValues(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Validate field
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
    
    // Update form validity
    validateForm();
  }, [validateField, validateForm]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    const allTouched = Object.keys(config).reduce((acc, key) => {
      acc[key as keyof T] = true;
      return acc;
    }, {} as Record<keyof T, boolean>);
    
    setTouched(allTouched);
    
    // Validate all fields
    const formIsValid = validateForm();
    
    if (formIsValid) {
      setIsSubmitting(true);
      
      try {
        await onSubmit(values);
      } catch (error) {
        console.error('Form submission error:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  }, [config, onSubmit, validateForm, values]);

  // Reset form to initial values
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
    setIsSubmitting(false);
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    handleChange,
    handleSubmit,
    setFieldValue,
    resetForm,
    validateForm
  };
}
