
import React, { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

import { Customer } from "@/lib/data";
import { useAppContext } from "@/contexts/AppContext";
import { toast } from "@/components/ui/sonner";


const customerSchema = z.object({
  name: z.string()
    .min(2, { message: "Name must be at least 2 characters" })
    .max(100, { message: "Name cannot exceed 100 characters" })
    .regex(/^[\p{L}\s'.,-]+$/u, { message: "Name can only contain letters, spaces, and basic punctuation" }),
  email: z.string()
    .email({ message: "Please enter a valid email address" })
    .max(100, { message: "Email cannot exceed 100 characters" })
    .optional()
    .or(z.literal('')),
  phone: z.string()
    .min(5, { message: "Phone number must be at least 5 characters" })
    .max(20, { message: "Phone number cannot exceed 20 characters" })
    .regex(/^[\d\s+().-]+$/, { message: "Phone number can only contain digits, spaces, and +().- characters" }),
  address: z.string()
    .max(500, { message: "Address cannot exceed 500 characters" })
    .optional(),
});

type CustomerFormValues = z.infer<typeof customerSchema>;

interface CustomerFormProps {
  customer?: Customer;
  onSuccess: (customer?: Customer) => void;
}

const CustomerForm: React.FC<CustomerFormProps> = ({ customer, onSuccess }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addCustomer, updateCustomer } = useAppContext();
  const isEditing = !!customer;

  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: customer?.name || "",
      email: customer?.email || "",
      phone: customer?.phone || "",
      address: customer?.address || "",
    },
  });

  const onSubmit = async (data: CustomerFormValues) => {
    setIsSubmitting(true);
    try {
      if (isEditing && customer) {
        // Update customer in Supabase and local state
        const updatedCustomer = await updateCustomer({
          ...customer,
          name: data.name,
          email: data.email,
          phone: data.phone,
          address: data.address ? data.address : undefined,
        });
        toast.success("Customer updated successfully", {
          description: `${updatedCustomer.name}'s information has been updated.`
        });
        onSuccess(updatedCustomer);
      } else {
        // Create customer in Supabase and add to local state
        const newCustomer = await addCustomer({
          name: data.name,
          email: data.email,
          phone: data.phone,
          address: data.address ? data.address : undefined,
        });
        toast.success("Customer created successfully", {
          description: `${newCustomer.name} has been added to your customers.`
        });
        form.reset();
        onSuccess(newCustomer);
      }
    } catch (error) {
      console.error("Error saving customer:", error);

      // Handle specific error types
      const errorMessage = error instanceof Error ? error.message : "Unknown error";

      if (errorMessage.includes("email already exists")) {
        form.setError("email", {
          type: "manual",
          message: "This email is already in use. Please use a different email address."
        });
        toast.error("Email already in use", {
          description: "Please use a different email address."
        });
      } else if (errorMessage.includes("phone") && errorMessage.includes("already exists")) {
        form.setError("phone", {
          type: "manual",
          message: "This phone number is already in use. Please use a different phone number."
        });
        toast.error("Phone number already in use", {
          description: "Please use a different phone number."
        });
      } else {
        toast.error("Failed to save customer", {
          description: errorMessage
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Full name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email (Optional)</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="Email address" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input placeholder="Phone number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter address"
                  {...field}
                  rows={3}
                />
              </FormControl>
              <FormDescription>
                Customer's full address for delivery or billing
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isSubmitting ? (isEditing ? "Updating..." : "Creating...") : (isEditing ? "Update Customer" : "Create Customer")}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default CustomerForm;
