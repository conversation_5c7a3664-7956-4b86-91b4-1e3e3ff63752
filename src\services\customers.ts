
import { supabase } from "@/integrations/supabase/client";
import type { Customer } from "@/lib/data";

/**
 * Search for customers by name, phone, or email
 * @param searchTerm The search term to look for
 * @returns Array of matching customers
 */
export async function searchCustomers(searchTerm: string): Promise<Customer[]> {
  try {
    if (!searchTerm.trim()) {
      return getCustomers();
    }

    const term = searchTerm.toLowerCase().trim();

    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .or(`name.ilike.%${term}%,phone.ilike.%${term}%${term ? ',email.ilike.%' + term + '%' : ''}`);

    if (error) {
      console.error("Error searching customers:", error);
      throw new Error(`Failed to search customers: ${error.message}`);
    }

    if (!data) {
      return [];
    }

    return data.map(item => ({
      id: item.id,
      name: item.name,
      email: item.email || undefined,
      phone: item.phone,
      address: item.address || undefined,
      createdAt: new Date(item.created_at),
      displayId: item.display_id || `C${item.id.substring(0, 4)}`
    }));
  } catch (error) {
    console.error("Unexpected error in searchCustomers:", error);
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred while searching customers");
  }
}

export async function getCustomers(): Promise<Customer[]> {
  try {
    const { data, error } = await supabase
      .from('customers')
      .select('*');

    if (error) {
      console.error("Error fetching customers:", error);
      throw new Error(`Failed to fetch customers: ${error.message}`);
    }

    if (!data) {
      return [];
    }

    return data.map(item => ({
      id: item.id,
      name: item.name,
      email: item.email || undefined,
      phone: item.phone,
      address: item.address || undefined,
      createdAt: new Date(item.created_at),
      displayId: item.display_id || `C${item.id.substring(0, 4)}`
    }));
  } catch (error) {
    console.error("Unexpected error in getCustomers:", error);
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred while fetching customers");
  }
}

export async function getCustomerById(id: string): Promise<Customer | null> {
  try {
    if (!id) {
      throw new Error("Customer ID is required");
    }

    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No customer found
      }
      console.error("Error fetching customer:", error);
      throw new Error(`Failed to fetch customer: ${error.message}`);
    }

    if (!data) {
      return null;
    }

    return {
      id: data.id,
      name: data.name,
      email: data.email || undefined,
      phone: data.phone,
      address: data.address || undefined,
      createdAt: new Date(data.created_at),
      displayId: data.display_id || `C${data.id.substring(0, 4)}`
    };
  } catch (error) {
    console.error("Unexpected error in getCustomerById:", error);
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred while fetching customer");
  }
}

export async function createCustomer(customer: Omit<Customer, "id" | "createdAt">): Promise<Customer> {
  try {
    // Validate required fields
    if (!customer.name) throw new Error("Customer name is required");
    if (!customer.phone) throw new Error("Customer phone is required");

    // Check if a customer with this phone number already exists
    const { data: existingCustomers, error: checkError } = await supabase
      .from('customers')
      .select('id, phone')
      .eq('phone', customer.phone);

    if (checkError) {
      console.error("Error checking for existing customer:", checkError);
    } else if (existingCustomers && existingCustomers.length > 0) {
      throw new Error(`A customer with this phone number already exists: ${customer.phone}`);
    }

    // Get the next customer number for display purposes only
    const { data: customers, error: countError } = await supabase
      .from('customers')
      .select('id')
      .order('created_at', { ascending: false });

    if (countError) {
      console.error("Error getting customer count:", countError);
    }

    // Insert the new customer (let the database generate the UUID)
    const { data, error } = await supabase
      .from('customers')
      .insert({
        name: customer.name,
        email: customer.email || null,
        phone: customer.phone,
        address: customer.address,
        display_id: `C${1000 + (customers?.length || 0) + 1}` // Generate a display ID like C1001
      })
      .select()
      .single();

    if (error) {
      // Handle specific error cases
      if (error.code === '23505') { // Unique constraint violation
        const errorMessage = error.message || '';
        if (errorMessage.includes('customers_email_unique')) {
          throw new Error(`A customer with this email already exists: ${customer.email}`);
        } else if (errorMessage.includes('customers_phone_unique')) {
          throw new Error(`A customer with this phone number already exists: ${customer.phone}`);
        } else {
          throw new Error(`A customer with these details already exists`);
        }
      }
      console.error("Error creating customer:", error);
      throw new Error(`Failed to create customer: ${error.message}`);
    }

    if (!data) {
      throw new Error("Failed to create customer: No data returned");
    }

    console.log("Customer created successfully:", data.id);

    return {
      id: data.id,
      name: data.name,
      email: data.email || undefined,
      phone: data.phone,
      address: data.address || undefined,
      createdAt: new Date(data.created_at),
      displayId: data.display_id || `C${data.id.substring(0, 4)}`
    };
  } catch (error) {
    console.error("Unexpected error in createCustomer:", error);
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred while creating customer");
  }
}

export async function updateCustomer(customer: Customer): Promise<Customer> {
  try {
    // Validate required fields
    if (!customer.id) throw new Error("Customer ID is required");
    if (!customer.name) throw new Error("Customer name is required");
    if (!customer.phone) throw new Error("Customer phone is required");

    const { data, error } = await supabase
      .from('customers')
      .update({
        name: customer.name,
        email: customer.email || null,
        phone: customer.phone,
        address: customer.address,
        display_id: customer.displayId // Preserve the display ID
      })
      .eq('id', customer.id)
      .select()
      .single();

    if (error) {
      // Handle specific error cases
      if (error.code === '23505') { // Unique constraint violation
        const errorMessage = error.message || '';
        if (errorMessage.includes('customers_email_unique')) {
          throw new Error(`A customer with this email already exists: ${customer.email}`);
        } else if (errorMessage.includes('customers_phone_unique')) {
          throw new Error(`A customer with this phone number already exists: ${customer.phone}`);
        } else {
          throw new Error(`A customer with these details already exists`);
        }
      }
      if (error.code === 'PGRST116') {
        throw new Error(`Customer not found with ID: ${customer.id}`);
      }
      console.error("Error updating customer:", error);
      throw new Error(`Failed to update customer: ${error.message}`);
    }

    if (!data) {
      throw new Error("Failed to update customer: No data returned");
    }

    console.log("Customer updated successfully:", data.id);

    return {
      id: data.id,
      name: data.name,
      email: data.email || undefined,
      phone: data.phone,
      address: data.address || undefined,
      createdAt: new Date(data.created_at),
      displayId: data.display_id || `C${data.id.substring(0, 4)}`
    };
  } catch (error) {
    console.error("Unexpected error in updateCustomer:", error);
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred while updating customer");
  }
}

export async function deleteCustomer(id: string): Promise<boolean> {
  try {
    if (!id) throw new Error("Customer ID is required");

    // Check if customer has any related repair jobs
    const { data: repairs, error: repairsError } = await supabase
      .from('repair_jobs')
      .select('id')
      .eq('customer_id', id);

    if (repairsError) {
      console.error("Error checking for related repair jobs:", repairsError);
      throw new Error(`Failed to check for related repair jobs: ${repairsError.message}`);
    }

    // If customer has repair jobs, don't allow deletion
    if (repairs && repairs.length > 0) {
      throw new Error(`Cannot delete customer with existing repair jobs. Please delete the repair jobs first.`);
    }

    // Check if customer has any related invoices
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('id')
      .eq('customer_id', id);

    if (invoicesError) {
      console.error("Error checking for related invoices:", invoicesError);
      throw new Error(`Failed to check for related invoices: ${invoicesError.message}`);
    }

    // If customer has invoices, don't allow deletion
    if (invoices && invoices.length > 0) {
      throw new Error(`Cannot delete customer with existing invoices. Please delete the invoices first.`);
    }

    // Delete the customer
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id);

    if (error) {
      console.error("Error deleting customer:", error);
      throw new Error(`Failed to delete customer: ${error.message}`);
    }

    return true;
  } catch (error) {
    console.error("Unexpected error in deleteCustomer:", error);
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred while deleting customer");
  }
}
