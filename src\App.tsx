
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/components/ui/sonner";
import { AppProvider } from "@/contexts/AppContext";
import { SettingsProvider } from "@/contexts/SettingsContext";
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import { setupGlobalErrorHandlers, setupConsoleCapturing } from '@/utils/errorLogger';
import { initVercelAnalytics, checkVercelAnalyticsAvailability } from '@/utils/vercelAnalyticsInit';
import Index from "./pages/Index";
import Customers from "./pages/Customers";
import Repairs from "./pages/Repairs";
import Inventory from "./pages/Inventory";
import Invoices from "./pages/Invoices";
import Reports from "./pages/Reports";
import NotFound from "./pages/NotFound";
import Settings from "./pages/Settings";

import { useEffect } from 'react';

// Show a warning if Supabase credentials are missing
if (!supabase) {
  console.warn('Supabase client is not initialized. Some features may not work correctly.');
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => {
  // Initialize error handlers and analytics
  useEffect(() => {
    // Set up global error handlers
    setupGlobalErrorHandlers();

    // Set up console capturing
    setupConsoleCapturing();

    // Initialize Vercel Analytics manually as a fallback
    initVercelAnalytics();

    // Check if Vercel Analytics is available
    const { vaAvailable, vercelAnalyticsAvailable, isProduction } = checkVercelAnalyticsAvailability();

    // Log initialization status
    console.info('Application initialized with error logging');
    console.info(`Vercel Analytics status: va=${vaAvailable}, vercelAnalytics=${vercelAnalyticsAvailable}, production=${isProduction}`);

    // Test analytics with a simple event
    if (vaAvailable) {
      window.va?.('event', {
        name: 'app_initialized',
        timestamp: new Date().toISOString()
      });
    }
  }, []);

  // Show a toast notification if Supabase is not properly configured
  if (!supabase) {
    setTimeout(() => {
      toast("Supabase configuration issue", {
        description: "The Supabase client could not be initialized. Some features may not work correctly.",
        duration: 5000,
      });
    }, 1000);
  }

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <SettingsProvider>
          <AppProvider>
          <Toaster position="top-right" expand={true} closeButton={true} />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/customers" element={<Customers />} />
              <Route path="/repairs" element={<Repairs />} />
              <Route path="/repairs/:id" element={<Repairs />} />
              <Route path="/inventory" element={<Inventory />} />
              <Route path="/invoices" element={<Invoices />} />
              <Route path="/invoices/:id" element={<Invoices />} />
              <Route path="/reports" element={<Reports />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
          </AppProvider>
        </SettingsProvider>
      </TooltipProvider>
      <Analytics />
      <SpeedInsights />
    </QueryClientProvider>
  );
};

export default App;
