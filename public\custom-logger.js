// Custom logging utility that works with the free Vercel plan
// This sends logs to our custom API endpoint which logs to Vercel server logs

(function() {
  // Create the logger object
  window.customLogger = {
    // Log an informational message
    info: function(message, details) {
      return sendLog('INFO', message, details);
    },

    // Log a warning
    warn: function(message, details) {
      return sendLog('WARNING', message, details);
    },

    // Log an error
    error: function(message, details) {
      return sendLog('ERROR', message, details);
    },

    // Log a page view
    pageView: function(path) {
      return sendLog('PAGE_VIEW', path || window.location.pathname, {
        url: window.location.href,
        referrer: document.referrer || 'direct',
        userAgent: navigator.userAgent
      });
    }
  };

  // Function to send logs to the API
  function sendLog(type, message, details) {
    // Create the payload
    const payload = {
      type: type,
      message: message,
      details: details || {
        url: window.location.href,
        userAgent: navigator.userAgent
      }
    };

    // Use sendBeacon for better reliability during page unload
    if (navigator.sendBeacon && (type === 'PAGE_UNLOAD' || window.isUnloading)) {
      try {
        const blob = new Blob([JSON.stringify(payload)], { type: 'application/json' });
        const success = navigator.sendBeacon('/api/custom-log', blob);
        if (!success) {
          console.log(`[FALLBACK_LOG] [${type}] ${message} (sendBeacon failed)`);
        }
        return Promise.resolve({ success: success });
      } catch (e) {
        console.log(`[FALLBACK_LOG] [${type}] ${message} (sendBeacon error: ${e.message})`);
        return Promise.resolve({ success: false, error: e.message });
      }
    }

    // For normal operation, use fetch
    return fetch('/api/custom-log', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload),
      // Add keepalive to help with page unload scenarios
      keepalive: true
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('Failed to send log');
      }
      return response.json();
    })
    .catch(error => {
      // If we can't log to the API, at least log to the console
      console.error('Failed to send log to API:', error);
      console.log(`[FALLBACK_LOG] [${type}] ${message}`);
      return { success: false, error: error.message };
    });
  }

  // Track page unload state
  window.isUnloading = false;

  // Automatically log page views
  window.addEventListener('load', function() {
    window.customLogger.pageView();
  });

  // Log page unload events
  window.addEventListener('beforeunload', function() {
    window.isUnloading = true;
    window.customLogger.info('Page unloaded', { type: 'PAGE_UNLOAD' });
  });

  // Capture unhandled errors
  window.addEventListener('error', function(event) {
    window.customLogger.error(event.message, {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error ? event.error.stack : null
    });
  });

  // Capture unhandled promise rejections
  window.addEventListener('unhandledrejection', function(event) {
    window.customLogger.error('Unhandled Promise Rejection', {
      reason: event.reason ? event.reason.toString() : 'Unknown reason'
    });
  });

  console.log('Custom logger initialized');
})();
