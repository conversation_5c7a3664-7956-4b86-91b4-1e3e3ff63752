// Type definitions for the custom logger

interface CustomLogger {
  /**
   * Log an informational message
   * @param message The message to log
   * @param details Optional details to include with the log
   */
  info(message: string, details?: Record<string, any>): Promise<any>;
  
  /**
   * Log a warning message
   * @param message The message to log
   * @param details Optional details to include with the log
   */
  warn(message: string, details?: Record<string, any>): Promise<any>;
  
  /**
   * Log an error message
   * @param message The message to log
   * @param details Optional details to include with the log
   */
  error(message: string, details?: Record<string, any>): Promise<any>;
  
  /**
   * Log a page view
   * @param path Optional path to log (defaults to current path)
   */
  pageView(path?: string): Promise<any>;
}

// Extend the Window interface to include our custom logger
interface Window {
  customLogger: CustomLogger;
  va?: (command: string, ...args: any[]) => void;
  vercelAnalytics?: {
    track: (event: string, properties?: any) => void;
  };
}
