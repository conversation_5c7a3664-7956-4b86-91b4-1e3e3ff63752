import React, { useState, useEffect } from "react";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { RepairJob, Invoice } from "@/lib/data";
import { formatINR } from "@/lib/currency";
import { useAppContext } from "@/contexts/AppContext";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

interface InvoiceFormProps {
  repairJob: RepairJob;
  onSuccess: (invoice: Invoice) => void;
}

// Define schema for invoice items
const invoiceItemSchema = z.object({
  description: z.string().min(1, { message: "Description is required" }),
  quantity: z.string()
    .min(1, { message: "Quantity is required" })
    .refine(val => !isNaN(parseFloat(val)), { message: "Must be a valid number" })
    .refine(val => parseFloat(val) > 0, { message: "Must be greater than 0" }),
  unitPrice: z.string()
    .min(1, { message: "Unit price is required" })
    .refine(val => !isNaN(parseFloat(val)), { message: "Must be a valid number" })
    .refine(val => parseFloat(val) >= 0, { message: "Must be a positive number" }),
  itemType: z.enum(["part", "labor", "service", "other"], {
    required_error: "Item type is required",
  }),
  discountAmount: z.string()
    .optional()
    .refine(val => !val || !isNaN(parseFloat(val)), { message: "Must be a valid number" })
    .refine(val => !val || parseFloat(val) >= 0, { message: "Must be a positive number" }),
  discountType: z.enum(["percentage", "fixed"]).optional(),
});

// Main invoice schema
const invoiceSchema = z.object({
  // Custom line items
  items: z.array(invoiceItemSchema),

  // Discount
  discountAmount: z.string()
    .optional()
    .refine(val => !val || !isNaN(parseFloat(val)), { message: "Must be a valid number" })
    .refine(val => !val || parseFloat(val) >= 0, { message: "Must be a positive number" }),
  discountType: z.enum(["percentage", "fixed"]).optional(),

  // Payment
  paymentMethod: z.enum(["cash", "card", "upi", "bank"], {
    required_error: "Payment method is required",
  }),
  paidAmount: z.string()
    .refine(val => !isNaN(parseFloat(val)), { message: "Must be a valid number" })
    .refine(val => parseFloat(val) >= 0, { message: "Must be a positive number" }),

  // Additional fields
  notes: z.string().optional(),
  terms: z.string().optional(),
  dueDate: z.string()
    .refine(val => !val || new Date(val) > new Date(), {
      message: "Due date must be in the future",
    }),
});

type FormValues = z.infer<typeof invoiceSchema>;

const InvoiceForm: React.FC<InvoiceFormProps> = ({ repairJob, onSuccess }) => {
  const { addInvoice } = useAppContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [subtotal, setSubtotal] = useState(0);
  const [tax, setTax] = useState(0);
  const [total, setTotal] = useState(0);
  const [balance, setBalance] = useState(0);
  const [paymentStatus, setPaymentStatus] = useState<"paid" | "partial" | "pending" | "overdue">("pending");

  // Initialize form with default values
  const { register, handleSubmit, watch, setValue, control, formState: { errors } } = useForm<FormValues>({
    resolver: zodResolver(invoiceSchema),
    defaultValues: {
      items: repairJob.partsUsed ? [
        // Add parts as line items
        ...repairJob.partsUsed.map(part => ({
          description: part.name,
          quantity: part.quantity.toString(),
          unitPrice: part.cost.toString(),
          itemType: "part" as const,
          discountAmount: "",
          discountType: undefined
        })),
        // Add default labor item
        {
          description: `Labor - ${repairJob.deviceType} Repair`,
          quantity: "1",
          unitPrice: repairJob.finalCost ? Math.round(repairJob.finalCost * 0.2).toString() : "1000",
          itemType: "labor" as const,
          discountAmount: "",
          discountType: undefined
        }
      ] : [
        // Default labor item if no parts
        {
          description: `Labor - ${repairJob.deviceType} Repair`,
          quantity: "1",
          unitPrice: "1000",
          itemType: "labor" as const,
          discountAmount: "",
          discountType: undefined
        }
      ],
      discountAmount: "",
      discountType: undefined,
      paymentMethod: "cash",
      paidAmount: "0",
      notes: "",
      terms: "Payment due upon receipt",
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }
  });

  // Use field array for dynamic items
  const { fields, append, remove } = useFieldArray({
    control,
    name: "items"
  });

  // Watch form values for calculations
  const formValues = watch();
  const paidAmount = parseFloat(formValues.paidAmount || "0");

  // Store repair job in window for debugging
  useEffect(() => {
    // Initialize debug objects
    if (!(window as any).__CONSOLE_LOGS__) {
      (window as any).__CONSOLE_LOGS__ = [];
    }

    if (repairJob) {
      (window as any).__DEBUG_REPAIR_JOB__ = repairJob;
      console.log('Repair job for invoice:', repairJob);
    }
  }, [repairJob]);

  useEffect(() => {
    // Calculate subtotal from all items
    const calculatedSubtotal = formValues.items?.reduce((sum, item) => {
      const quantity = parseFloat(item.quantity || "0");
      const unitPrice = parseFloat(item.unitPrice || "0");
      const itemTotal = quantity * unitPrice;
      return sum + itemTotal;
    }, 0) || 0;

    setSubtotal(calculatedSubtotal);

    // Apply discount if any
    let discountedAmount = calculatedSubtotal;
    if (formValues.discountAmount && parseFloat(formValues.discountAmount) > 0) {
      const discountValue = parseFloat(formValues.discountAmount);
      if (formValues.discountType === "percentage") {
        discountedAmount = calculatedSubtotal * (1 - (discountValue / 100));
      } else if (formValues.discountType === "fixed") {
        discountedAmount = calculatedSubtotal - discountValue;
      }
    }

    // No tax calculation - total is the discounted amount
    setTax(0);

    // Calculate total (same as discounted amount since no tax)
    const calculatedTotal = discountedAmount;
    setTotal(calculatedTotal);

    // Calculate balance
    const calculatedBalance = calculatedTotal - paidAmount;
    setBalance(calculatedBalance);

    // Determine payment status
    if (calculatedBalance <= 0) {
      setPaymentStatus("paid");
    } else if (paidAmount > 0) {
      setPaymentStatus("partial");
    } else {
      setPaymentStatus("pending");
    }

    // Check if invoice is overdue
    if (paymentStatus !== "paid" && formValues.dueDate) {
      const dueDate = new Date(formValues.dueDate);
      if (dueDate < new Date()) {
        setPaymentStatus("overdue");
      }
    }
  }, [formValues, paidAmount]);

  const onSubmit = async (data: FormValues) => {
    console.log("Form submission started");
    setIsSubmitting(true);
    try {
      console.log("Form data:", data);
      // Convert form items to invoice items
      const invoiceItems = data.items.map(item => ({
        description: item.description,
        quantity: parseFloat(item.quantity),
        unitPrice: parseFloat(item.unitPrice),
        total: parseFloat(item.quantity) * parseFloat(item.unitPrice),
        itemType: item.itemType,
        discountAmount: item.discountAmount ? parseFloat(item.discountAmount) : undefined,
        discountType: item.discountType
      }));

      // Create invoice data
      console.log("Creating invoice with items:", invoiceItems);
      console.log("Repair job:", repairJob);
      (window as any).__CONSOLE_LOGS__.push({ type: 'info', message: 'Creating invoice', items: invoiceItems, repairJob });

      console.log('[DEBUG] InvoiceForm - Repair job:', repairJob);
      console.log('[DEBUG] InvoiceForm - Repair job ID:', repairJob.id);
      console.log('[DEBUG] InvoiceForm - Repair job DB ID:', repairJob.dbId);

      const repairDbId = repairJob.dbId || repairJob.id;

      // Double-check if an invoice already exists for this repair job
      const { data: existingInvoices, error: checkError } = await supabase
        .from('invoices')
        .select('id, invoice_number')
        .eq('repair_job_id', repairDbId);

      if (checkError) {
        console.error("Error checking for existing invoices:", checkError);
      } else if (existingInvoices && existingInvoices.length > 0) {
        // We're already in the invoice creation form, so this is a double-check
        // Just log a warning but proceed with creation
        console.warn(`Creating another invoice for repair job ${repairDbId}. Existing invoice(s): ${existingInvoices.length}`);
      }

      const invoiceData = {
        repairJobId: repairDbId,
        customerId: repairJob.customerId,
        customerName: repairJob.customerName,
        items: invoiceItems,
        subtotal: subtotal,
        discountAmount: data.discountAmount ? parseFloat(data.discountAmount) : undefined,
        discountType: data.discountType,
        total: total,
        paidAmount: parseFloat(data.paidAmount),
        balance: balance,
        paymentStatus: paymentStatus,
        // Include payment method for the service to create a payment record
        paymentMethod: data.paymentMethod as 'cash' | 'card' | 'upi' | 'bank',
        notes: data.notes,
        terms: data.terms,
        dueDate: data.dueDate ? new Date(data.dueDate) : undefined
      };

      // Save to Supabase
      console.log("Sending invoice data to service:", invoiceData);
      (window as any).__CONSOLE_LOGS__.push({ type: 'info', message: 'Sending invoice data to service', data: invoiceData });

      const savedInvoice = await addInvoice(invoiceData);
      console.log("Invoice created successfully:", savedInvoice);
      (window as any).__CONSOLE_LOGS__.push({ type: 'success', message: 'Invoice created successfully', data: savedInvoice });

      onSuccess(savedInvoice);
    } catch (error) {
      console.error("Error creating invoice:", error);
      // Log more details about the error
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
        (window as any).__CONSOLE_LOGS__.push({
          type: 'error',
          message: 'Error creating invoice',
          errorMessage: error.message,
          errorStack: error.stack
        });
      } else {
        (window as any).__CONSOLE_LOGS__.push({
          type: 'error',
          message: 'Error creating invoice',
          error
        });
      }
      toast.error(error instanceof Error ? error.message : "Failed to create invoice");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add a new line item
  const addLineItem = () => {
    append({
      description: "",
      quantity: "1",
      unitPrice: "0",
      itemType: "service",
      discountAmount: "",
      discountType: undefined
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Line Items Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Invoice Items</h3>
          <Button
            type="button"
            size="sm"
            variant="outline"
            onClick={addLineItem}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" /> Add Item
          </Button>
        </div>

        <div className="border rounded-md p-4 space-y-6">
          {fields.map((field, index) => (
            <div key={field.id} className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">Item #{index + 1}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => remove(index)}
                  disabled={fields.length <= 1}
                >
                  <Trash2 className="h-4 w-4" />
                  <span className="sr-only">Remove</span>
                </Button>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-12 gap-4 sm:gap-2">
                <div className="sm:col-span-12 md:col-span-4">
                  <Label htmlFor={`items.${index}.description`}>Description</Label>
                  <Input
                    id={`items.${index}.description`}
                    {...register(`items.${index}.description`)}
                  />
                  {errors.items?.[index]?.description && (
                    <p className="text-sm text-red-500 mt-1">{errors.items?.[index]?.description?.message}</p>
                  )}
                </div>

                <div className="sm:col-span-4 md:col-span-2">
                  <Label htmlFor={`items.${index}.quantity`}>Quantity</Label>
                  <Input
                    id={`items.${index}.quantity`}
                    type="number"
                    min="1"
                    step="1"
                    {...register(`items.${index}.quantity`)}
                  />
                  {errors.items?.[index]?.quantity && (
                    <p className="text-sm text-red-500 mt-1">{errors.items?.[index]?.quantity?.message}</p>
                  )}
                </div>

                <div className="sm:col-span-4 md:col-span-3">
                  <Label htmlFor={`items.${index}.unitPrice`}>Unit Price</Label>
                  <Input
                    id={`items.${index}.unitPrice`}
                    type="number"
                    min="0"
                    step="1"
                    {...register(`items.${index}.unitPrice`)}
                  />
                  {errors.items?.[index]?.unitPrice && (
                    <p className="text-sm text-red-500 mt-1">{errors.items?.[index]?.unitPrice?.message}</p>
                  )}
                </div>

                <div className="sm:col-span-4 md:col-span-3">
                  <Label htmlFor={`items.${index}.itemType`}>Type</Label>
                  <Select
                    defaultValue={field.itemType}
                    onValueChange={(value) => setValue(`items.${index}.itemType`, value as any)}
                  >
                    <SelectTrigger id={`items.${index}.itemType`}>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="part">Part</SelectItem>
                      <SelectItem value="labor">Labor</SelectItem>
                      <SelectItem value="service">Service</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.items?.[index]?.itemType && (
                    <p className="text-sm text-red-500 mt-1">{errors.items?.[index]?.itemType?.message}</p>
                  )}
                </div>
              </div>

              {index < fields.length - 1 && <div className="border-t my-4"></div>}
            </div>
          ))}
        </div>
      </div>

      {/* Invoice Details */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Invoice Details</h3>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          <div className="sm:col-span-2">
            <Label htmlFor="discountAmount">Discount</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                id="discountAmount"
                type="number"
                min="0"
                placeholder="Amount"
                {...register("discountAmount")}
              />
              <Select
                defaultValue={formValues.discountType || "fixed"}
                onValueChange={(value) => setValue("discountType", value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fixed">Fixed (₹)</SelectItem>
                  <SelectItem value="percentage">Percentage (%)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {errors.discountAmount && (
              <p className="text-sm text-red-500 mt-1">{errors.discountAmount.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="paymentMethod">Payment Method</Label>
            <Select
              defaultValue="cash"
              onValueChange={(value) => setValue("paymentMethod", value as any)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cash">Cash</SelectItem>
                <SelectItem value="card">Card</SelectItem>
                <SelectItem value="upi">UPI</SelectItem>
                <SelectItem value="bank">Bank Transfer</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Additional notes for the invoice"
              {...register("notes")}
              className="min-h-[100px]"
            />
          </div>

          <div>
            <Label htmlFor="terms">Terms & Conditions</Label>
            <Textarea
              id="terms"
              placeholder="Payment terms and conditions"
              {...register("terms")}
              className="min-h-[100px]"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="paidAmount">Paid Amount</Label>
            <Input
              id="paidAmount"
              type="number"
              min="0"
              step="1"
              {...register("paidAmount")}
            />
            {errors.paidAmount && (
              <p className="text-sm text-red-500 mt-1">{errors.paidAmount.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="dueDate">Due Date</Label>
            <Input
              id="dueDate"
              type="date"
              {...register("dueDate")}
            />
            {errors.dueDate && (
              <p className="text-sm text-red-500 mt-1">{errors.dueDate.message}</p>
            )}
          </div>
        </div>

        <div className="border rounded-md p-4 mt-4 space-y-2">
          <div className="flex justify-between">
            <span>Subtotal:</span>
            <span>{formatINR(subtotal)}</span>
          </div>
          <div className="flex justify-between font-bold">
            <span>Total:</span>
            <span>{formatINR(total)}</span>
          </div>
          <div className="flex justify-between">
            <span>Paid Amount:</span>
            <span>{formatINR(paidAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Balance:</span>
            <span>{formatINR(balance)}</span>
          </div>
          <div className="flex justify-between">
            <span>Payment Status:</span>
            <span
              className={`px-2 py-1 rounded-full text-xs ${
                paymentStatus === "paid"
                  ? "bg-green-100 text-green-700"
                  : paymentStatus === "partial"
                  ? "bg-yellow-100 text-yellow-700"
                  : paymentStatus === "overdue"
                  ? "bg-red-100 text-red-700"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {paymentStatus === "paid"
                ? "Paid"
                : paymentStatus === "partial"
                ? "Partial"
                : paymentStatus === "overdue"
                ? "Overdue"
                : "Pending"}
            </span>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isSubmitting ? "Creating Invoice..." : "Create Invoice"}
        </Button>
      </div>
    </form>
  );
};

export default InvoiceForm;
