import React from "react";
import { Calendar } from "lucide-react";

interface SimpleDatePickerProps {
  label: string;
  value: Date;
  onChange: (date: Date) => void;
}

/**
 * A very basic date picker that uses the native HTML date input
 * This is a temporary solution until we can fix the module loading issues
 */
export function SimpleDatePicker({ label, value, onChange }: SimpleDatePickerProps) {
  // Format date as YYYY-MM-DD for input
  const formatDateForInput = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Handle date change from input
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(e.target.value);
    if (!isNaN(newDate.getTime())) {
      onChange(newDate);
    }
  };

  return (
    <div className="flex items-center space-x-2 border rounded-md px-3 py-2 bg-background">
      <Calendar className="h-4 w-4 text-muted-foreground" />
      <input
        type="date"
        aria-label={label}
        value={formatDateForInput(value)}
        onChange={handleDateChange}
        className="border-0 bg-transparent focus:outline-none focus:ring-0 p-0 text-sm"
      />
    </div>
  );
}
