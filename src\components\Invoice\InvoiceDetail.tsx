import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { formatINR } from "@/lib/currency";
import { Invoice } from "@/lib/data";
import { Printer, FileDown, Plus, CreditCard, Trash2, AlertTriangle } from "lucide-react";
import { generateInvoicePDF } from "@/lib/pdf";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { useAppContext } from "@/contexts/AppContext";
import { toast } from "@/components/ui/sonner";
import { useSettings } from "@/contexts/SettingsContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface InvoiceDetailProps {
  invoice: Invoice;
  onRefresh?: () => void;
}

const InvoiceDetail: React.FC<InvoiceDetailProps> = ({ invoice, onRefresh }) => {
  const { addPayment, deleteInvoice } = useAppContext();
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState("");
  const [paymentMethod, setPaymentMethod] = useState<"cash" | "card" | "upi" | "bank">("cash");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { settings } = useSettings();

  const handlePrint = () => {
    try {
      // Create a new window with specific options
      const printWindow = window.open('', '_blank', 'width=800,height=600,toolbar=0,scrollbars=1,status=0');

      if (!printWindow) {
        toast.error('Failed to open print window. Pop-up might be blocked.');
        return;
      }

      // Format the date
      const formattedDate = invoice.createdAt.toLocaleDateString('en-IN');
      const formattedDueDate = invoice.dueDate ? invoice.dueDate.toLocaleDateString('en-IN') : '';

      // Generate payment history HTML
      let paymentHistoryHTML = '';
      if (invoice.payments && invoice.payments.length > 0) {
        paymentHistoryHTML = `
          <div class="payment-history">
            <h3 style="margin-bottom: 3px;">Payment History</h3>
            <table class="payment-table" style="margin-bottom: 10px;">
              <thead>
                <tr>
                  <th style="padding: 5px;">Date</th>
                  <th style="padding: 5px;">Method</th>
                  <th style="padding: 5px;">Amount</th>
                  <th style="padding: 5px;">Receipt #</th>
                </tr>
              </thead>
              <tbody>
                ${invoice.payments.map(payment => `
                  <tr>
                    <td style="padding: 5px;">${payment.paymentDate.toLocaleDateString('en-IN')}</td>
                    <td style="padding: 5px;">${payment.paymentMethod.toUpperCase()}</td>
                    <td class="amount" style="padding: 5px;">Rs. ${payment.amount.toLocaleString('en-IN')}</td>
                    <td style="padding: 5px;">${payment.receiptNumber || 'N/A'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        `;
      }

      // Generate items HTML
      const itemsHTML = invoice.items.map(item => `
        <tr>
          <td>${item.description}</td>
          <td class="text-right">${item.quantity}</td>
          <td class="text-right">Rs. ${item.unitPrice.toLocaleString('en-IN')}</td>
          <td class="text-right">Rs. ${item.total.toLocaleString('en-IN')}</td>
        </tr>
      `).join('');

      // Create the HTML content
      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Invoice #${invoice.invoiceNumber || invoice.id}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                color: #333;
              }
              .invoice-container {
                max-width: 800px;
                margin: 0 auto;
              }
              .header {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
              }
              .company-info {
                flex: 1;
              }
              .invoice-info {
                text-align: right;
                flex: 1;
              }
              h1, h2, h3 {
                margin: 0 0 5px 0;
              }
              .customer-info {
                margin-bottom: 10px;
              }
              table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
              }
              th, td {
                padding: 8px;
                text-align: left;
                border-bottom: 1px solid #ddd;
              }
              th {
                background-color: #f8f8f8;
                font-weight: bold;
              }
              .text-right {
                text-align: right;
              }
              .totals {
                width: 100%;
                max-width: 400px;
                margin-left: auto;
                background-color: #f9f9f9;
                padding: 15px;
              }
              .totals-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
              }
              .total-line {
                font-weight: bold;
                font-size: 1.1em;
                margin-top: 8px;
                margin-bottom: 8px;
              }
              .payment-line {
                margin-top: 8px;
              }
              .footer {
                margin-top: 20px;
                text-align: center;
                font-size: 0.9em;
                color: #666;
              }
              .payment-history {
                margin-top: 15px;
              }
              .payment-table th, .payment-table td {
                padding: 8px;
              }
              .amount {
                text-align: right;
              }
              @media print {
                body {
                  padding: 0;
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
                }
                .invoice-container {
                  width: 100%;
                }
              }
            </style>
          </head>
          <body>
            <div class="invoice-container">
              <div class="header">
                <div class="company-info">
                  <h1>${settings?.company?.companyName || 'Mobile Repair Shop'}</h1>
                  <p>${settings?.company?.address || '123 Repair Street, Tech City'}</p>
                  <p>Phone: ${settings?.company?.phone || '+91 98765 43210'}</p>
                  <p>Email: ${settings?.company?.email || '<EMAIL>'}</p>
                </div>
                <div class="invoice-info">
                  <h2>Invoice #${invoice.invoiceNumber || invoice.id}</h2>
                  <p>Date: ${formattedDate}</p>
                  ${formattedDueDate ? `<p>Due Date: ${formattedDueDate}</p>` : ''}
                  <p>Status: ${invoice.paymentStatus.charAt(0).toUpperCase() + invoice.paymentStatus.slice(1)}</p>
                </div>
              </div>

              <div class="customer-info">
                <h3>Customer Information</h3>
                <p style="margin: 5px 0">Name: ${invoice.customerName}</p>
                <p style="margin: 5px 0">Customer ID: ${invoice.customerId}</p>
                <p style="margin: 5px 0">Repair Job ID: ${invoice.repairJobId}</p>
              </div>

              <h3 style="margin-bottom: 5px;">Invoice Items</h3>
              <table>
                <thead>
                  <tr>
                    <th>Description</th>
                    <th class="text-right">Quantity</th>
                    <th class="text-right">Unit Price</th>
                    <th class="text-right">Total</th>
                  </tr>
                </thead>
                <tbody>
                  ${itemsHTML}
                </tbody>
              </table>

              <div class="totals">
                <div class="totals-row">
                  <span>Subtotal:</span>
                  <span>Rs. ${invoice.subtotal.toLocaleString('en-IN')}</span>
                </div>
                <div class="totals-row total-line">
                  <span>Total:</span>
                  <span>Rs. ${invoice.total.toLocaleString('en-IN')}</span>
                </div>
                <div class="totals-row payment-line">
                  <span>Paid Amount:</span>
                  <span>Rs. ${invoice.paidAmount.toLocaleString('en-IN')}</span>
                </div>
                <div class="totals-row">
                  <span>Balance:</span>
                  <span>Rs. ${invoice.balance.toLocaleString('en-IN')}</span>
                </div>
              </div>

              ${paymentHistoryHTML}

              ${invoice.notes ? `
                <div class="notes" style="margin-top: 10px;">
                  <h3 style="margin-bottom: 3px;">Notes</h3>
                  <p style="margin: 0;">${invoice.notes}</p>
                </div>
              ` : ''}

              ${invoice.terms ? `
                <div class="terms" style="margin-top: 10px;">
                  <h3 style="margin-bottom: 3px;">Terms & Conditions</h3>
                  <p style="margin: 0;">${invoice.terms}</p>
                </div>
              ` : ''}

              <div class="footer">
                <p style="margin: 2px 0;">Thank you for your business!</p>
                <p style="margin: 2px 0;">This is a computer-generated invoice and does not require a signature.</p>
                <p style="margin: 2px 0;">For any queries, please contact us at ${settings?.company?.email || '<EMAIL>'}</p>
              </div>
            </div>
            <script>
              // Auto print when loaded
              window.onload = function() {
                window.print();
                // Close the window after printing (or after cancel)
                window.onfocus = function() {
                  setTimeout(function() {
                    window.close();
                  }, 500);
                };
              };
            </script>
          </body>
        </html>
      `;

      // Write the HTML content to the new window using document.open/document.close
      printWindow.document.open();
      printWindow.document.write(htmlContent);
      printWindow.document.close();
    } catch (error) {
      console.error("Print failed:", error);
      toast.error("Failed to print invoice");
    }
  };

  const handleDownloadPDF = async () => {
    try {
      await generateInvoicePDF(invoice);
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF");
    }
  };

  const handleAddPayment = async () => {
    const amount = parseFloat(paymentAmount);

    // Validate payment amount
    if (!paymentAmount || amount <= 0) {
      toast.error("Please enter a valid payment amount");
      return;
    }

    // Check if payment amount exceeds remaining balance
    if (amount > invoice.balance) {
      toast.error(`Payment amount exceeds the remaining balance. Maximum payment allowed: ${formatINR(invoice.balance)}`);
      return;
    }

    setIsSubmitting(true);
    try {
      // Generate a receipt number
      const receiptNumber = `REC-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`;

      await addPayment(invoice.id, {
        amount: amount,
        paymentMethod,
        paymentDate: new Date(),
        notes: "Additional payment",
        receiptNumber: receiptNumber
      });

      setIsPaymentDialogOpen(false);
      setPaymentAmount("");

      // Refresh the invoice data if callback provided
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error("Error adding payment:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add payment");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteInvoice = async () => {
    setIsSubmitting(true);
    try {
      await deleteInvoice(invoice.id);
      setIsDeleteDialogOpen(false);

      // Refresh the parent component if callback provided
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error("Error deleting invoice:", error);
      toast.error("Failed to delete invoice");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Invoice Header - Company and Invoice Info */}
      <div className="flex justify-between items-start">


        {/* Invoice Info */}
        <div>
          <h2 className="text-2xl font-bold">Invoice #{invoice.invoiceNumber || invoice.id}</h2>
          <p className="text-muted-foreground">
            Date: {invoice.createdAt.toLocaleDateString("en-IN")}
          </p>
          {invoice.dueDate && (
            <p className="text-muted-foreground">
              Due Date: {invoice.dueDate.toLocaleDateString("en-IN")}
            </p>
          )}

        </div>

        {/* Action Buttons - Hidden when printing */}
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownloadPDF}>
            <FileDown className="mr-2 h-4 w-4" />
            Download PDF
          </Button>
          <Button variant="destructive" size="sm" onClick={() => setIsDeleteDialogOpen(true)}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p>
                <span className="font-medium">Name:</span> {invoice.customerName}
              </p>
              <p>
                <span className="font-medium">Customer ID:</span> {invoice.customerId.startsWith('C') ? invoice.customerId : `C${invoice.customerId.substring(0, 4)}`}
              </p>
              <p>
                <span className="font-medium">Repair Job ID:</span> {invoice.repairJobId}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p>
                <span className="font-medium">Status:</span>{" "}
                <Badge
                  variant={invoice.paymentStatus === "paid" ? "default" :
                          invoice.paymentStatus === "partial" ? "secondary" :
                          invoice.paymentStatus === "overdue" ? "destructive" : "outline"}
                  className={invoice.paymentStatus === "paid" ? "bg-green-500 hover:bg-green-500/80" :
                            invoice.paymentStatus === "partial" ? "bg-amber-500 hover:bg-amber-500/80" :
                            undefined}
                >
                  {invoice.paymentStatus === "paid"
                    ? "Paid"
                    : invoice.paymentStatus === "partial"
                    ? "Partial"
                    : invoice.paymentStatus === "overdue"
                    ? "Overdue"
                    : "Pending"}
                </Badge>
              </p>
              <p>
                <span className="font-medium">Amount Paid:</span>{" "}
                {formatINR(invoice.paidAmount)}
              </p>
              <p>
                <span className="font-medium">Balance:</span> {formatINR(invoice.balance)}
              </p>

              {/* Payment buttons for partial or pending invoices - hidden when printing */}
              {(invoice.paymentStatus === "partial" || invoice.paymentStatus === "pending" || invoice.paymentStatus === "overdue") && (
                <div className="mt-4 space-y-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setIsPaymentDialogOpen(true)}
                    className="w-full flex items-center justify-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Payment
                  </Button>

                  <Button
                    size="sm"
                    variant="default"
                    onClick={() => {
                      // Set the payment amount to the remaining balance
                      setPaymentAmount(invoice.balance.toString());
                      setIsPaymentDialogOpen(true);
                    }}
                    className="w-full flex items-center justify-center gap-2"
                  >
                    <CreditCard className="h-4 w-4" />
                    Pay Full Amount ({formatINR(invoice.balance)})
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Invoice Items</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop and Print view - Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="py-3 px-4 text-left">Description</th>
                  <th className="py-3 px-4 text-right">Quantity</th>
                  <th className="py-3 px-4 text-right">Unit Price</th>
                  <th className="py-3 px-4 text-right">Total</th>
                </tr>
              </thead>
              <tbody>
                {invoice.items.map((item, index) => (
                  <tr key={index} className="border-b">
                    <td className="py-3 px-4">{item.description}</td>
                    <td className="py-3 px-4 text-right">{item.quantity}</td>
                    <td className="py-3 px-4 text-right">{formatINR(item.unitPrice)}</td>
                    <td className="py-3 px-4 text-right">{formatINR(item.total)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile view - Cards (hidden when printing) */}
          <div className="md:hidden space-y-3">
            {invoice.items.map((item, index) => (
              <div key={index} className="border rounded-md p-3">
                <div className="font-medium mb-1">{item.description}</div>
                <div className="grid grid-cols-3 text-sm gap-1">
                  <div>
                    <span className="text-muted-foreground">Qty:</span> {item.quantity}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Price:</span> {formatINR(item.unitPrice)}
                  </div>
                  <div className="text-right font-medium">
                    {formatINR(item.total)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter>
          <div className="ml-auto space-y-2 w-full sm:w-auto">
            <div className="flex justify-between w-full sm:w-[250px]">
              <span>Subtotal:</span>
              <span>{formatINR(invoice.subtotal)}</span>
            </div>
            {invoice.discountAmount && invoice.discountType && (
              <div className="flex justify-between w-full sm:w-[250px]">
                <span>Discount ({invoice.discountType === 'percentage' ? `${invoice.discountAmount}%` : 'Fixed'}):</span>
                <span>-{formatINR(invoice.discountType === 'percentage' ?
                  (invoice.subtotal * (invoice.discountAmount / 100)) :
                  invoice.discountAmount)}</span>
              </div>
            )}
            <div className="flex justify-between w-full sm:w-[250px] font-bold">
              <span>Total:</span>
              <span>{formatINR(invoice.total)}</span>
            </div>
          </div>
        </CardFooter>
      </Card>

      {/* Payment History */}
      {invoice.payments && invoice.payments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Payment History</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Desktop and Print view - Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="py-3 px-4 text-left">Date</th>
                    <th className="py-3 px-4 text-left">Method</th>
                    <th className="py-3 px-4 text-right">Amount</th>
                    <th className="py-3 px-4 text-left">Receipt</th>
                  </tr>
                </thead>
                <tbody>
                  {invoice.payments.map((payment, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-3 px-4">{payment.paymentDate.toLocaleDateString("en-IN")}</td>
                      <td className="py-3 px-4">{payment.paymentMethod.toUpperCase()}</td>
                      <td className="py-3 px-4 text-right">{formatINR(payment.amount)}</td>
                      <td className="py-3 px-4">{payment.receiptNumber || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile view - Cards (hidden when printing) */}
            <div className="md:hidden space-y-3">
              {invoice.payments.map((payment, index) => (
                <div key={index} className="border rounded-md p-3">
                  <div className="flex justify-between items-center mb-1">
                    <div className="font-medium">{payment.paymentDate.toLocaleDateString("en-IN")}</div>
                    <div className="text-right font-medium">{formatINR(payment.amount)}</div>
                  </div>
                  <div className="grid grid-cols-2 text-sm gap-1">
                    <div>
                      <span className="text-muted-foreground">Method:</span> {payment.paymentMethod.toUpperCase()}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Receipt:</span> {payment.receiptNumber || '-'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notes and Terms */}
      {(invoice.notes || invoice.terms) && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {invoice.notes && (
              <div>
                <h3 className="font-medium mb-1">Notes</h3>
                <p className="text-sm">{invoice.notes}</p>
              </div>
            )}
            {invoice.terms && (
              <div>
                <h3 className="font-medium mb-1">Terms & Conditions</h3>
                <p className="text-sm">{invoice.terms}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}



      {/* Add Payment Dialog */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Payment</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="paymentAmount">Payment Amount</Label>
              <Input
                id="paymentAmount"
                type="number"
                min="0"
                max={invoice.balance}
                step="100"
                value={paymentAmount}
                onChange={(e) => setPaymentAmount(e.target.value)}
                placeholder={`Enter amount (max: ${formatINR(invoice.balance)})`}
              />
            </div>
            <div>
              <Label htmlFor="paymentMethod">Payment Method</Label>
              <Select
                value={paymentMethod}
                onValueChange={(value) => setPaymentMethod(value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="card">Card</SelectItem>
                  <SelectItem value="upi">UPI</SelectItem>
                  <SelectItem value="bank">Bank Transfer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPaymentDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleAddPayment} disabled={isSubmitting}>
              {isSubmitting ? "Processing..." : "Add Payment"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Invoice Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              Confirm Delete Invoice
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete invoice <strong>#{invoice.invoiceNumber}</strong>?</p>
            <p className="mt-2 text-muted-foreground">This action cannot be undone. All invoice items and payment records will be permanently deleted.</p>

            {invoice.paymentStatus === 'paid' || invoice.paymentStatus === 'partial' ? (
              <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md text-amber-800">
                <p className="text-sm font-medium">Warning: This invoice has payments associated with it.</p>
                <p className="text-sm">Deleting this invoice will also delete all payment records.</p>
              </div>
            ) : null}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteInvoice} disabled={isSubmitting}>
              {isSubmitting ? "Deleting..." : "Delete Invoice"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InvoiceDetail;
