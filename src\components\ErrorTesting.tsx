import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useErrorLogger } from '@/hooks';
import { logError, ErrorSeverity } from '@/utils/errorLogger';

/**
 * Component for testing different error scenarios
 * This is only for development/testing and should not be included in production
 */
const ErrorTesting: React.FC = () => {
  const { logError: logComponentError, withErrorLogging } = useErrorLogger('ErrorTesting');
  const [count, setCount] = useState(0);

  // Test a simple error log
  const testSimpleError = () => {
    logError('This is a test error message', ErrorSeverity.ERROR, {
      component: 'ErrorTesting',
      action: 'testSimpleError'
    });
  };

  // Test a console error
  const testConsoleError = () => {
    console.error('This is a test console error');
  };

  // Test a console warning
  const testConsoleWarning = () => {
    console.warn('This is a test console warning');
  };

  // Test an error with a large payload (should be handled gracefully)
  const testLargePayload = () => {
    const largeObject = {
      message: 'Large payload test',
      data: Array(1000).fill('test data string that is quite long to simulate a large payload')
    };
    
    logError('Error with large payload', ErrorSeverity.WARNING, {
      component: 'ErrorTesting',
      action: 'testLargePayload',
      additionalData: largeObject
    });
  };

  // Test an error in an async function
  const testAsyncError = withErrorLogging(async () => {
    // Simulate an API call that fails
    await new Promise(resolve => setTimeout(resolve, 500));
    throw new Error('Async operation failed');
  }, 'testAsyncError');

  // Test a runtime error that will be caught by the error boundary
  const testRuntimeError = () => {
    // This will cause a runtime error
    const obj = null;
    // @ts-ignore - Intentional error for testing
    obj.nonExistentMethod();
  };

  // Test a recursive component error (should be prevented by our safeguards)
  const testRecursiveError = () => {
    const generateRecursiveObject = (): any => {
      const obj: any = {};
      obj.child = { parent: obj };
      return obj;
    };

    const recursiveObj = generateRecursiveObject();
    logComponentError('Testing recursive object handling', {
      action: 'testRecursiveError',
      additionalData: { recursiveObj }
    });
  };

  // Test multiple rapid errors
  const testMultipleErrors = () => {
    for (let i = 0; i < 60; i++) {
      logError(`Test error ${i}`, ErrorSeverity.INFO, {
        component: 'ErrorTesting',
        action: 'testMultipleErrors',
        additionalData: { index: i }
      });
    }
  };

  // Test state update error
  const testStateUpdateError = () => {
    setCount(prevCount => {
      if (prevCount > 0) {
        throw new Error('Error during state update');
      }
      return prevCount + 1;
    });
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>Error Logging Test Panel</CardTitle>
        <CardDescription>
          Use these buttons to test different error scenarios and verify that the error logging system is working correctly.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button onClick={testSimpleError} variant="outline">
            Test Simple Error
          </Button>
          
          <Button onClick={testConsoleError} variant="outline">
            Test Console Error
          </Button>
          
          <Button onClick={testConsoleWarning} variant="outline">
            Test Console Warning
          </Button>
          
          <Button onClick={testLargePayload} variant="outline">
            Test Large Payload
          </Button>
          
          <Button onClick={() => testAsyncError()} variant="outline">
            Test Async Error
          </Button>
          
          <Button onClick={testRecursiveError} variant="outline">
            Test Recursive Object
          </Button>
          
          <Button onClick={testMultipleErrors} variant="outline">
            Test Multiple Errors
          </Button>
          
          <Button onClick={testStateUpdateError} variant="outline">
            Test State Update Error (Count: {count})
          </Button>
        </div>
        
        <Separator className="my-6" />
        
        <div className="bg-destructive/10 p-4 rounded-md">
          <h3 className="font-semibold text-destructive mb-2">Warning: Runtime Error Test</h3>
          <p className="text-sm mb-4">
            This button will cause a runtime error that should be caught by the Error Boundary component.
            The page may temporarily show an error UI.
          </p>
          <Button onClick={testRuntimeError} variant="destructive">
            Test Runtime Error
          </Button>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col items-start">
        <p className="text-sm text-muted-foreground">
          After testing, check the browser console and your Vercel Analytics dashboard to verify that errors are being logged correctly.
        </p>
      </CardFooter>
    </Card>
  );
};

export default ErrorTesting;
