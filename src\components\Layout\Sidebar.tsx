import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  Home,
  Users,
  Wrench,
  Package,
  FileText,
  Settings,
  Menu,
  X,
  BarC<PERSON>
} from "lucide-react";

type SidebarProps = {
  isOpen: boolean;
  toggleSidebar: () => void;
};

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();

  const links = [
    { name: "Dashboard", path: "/", icon: Home },
    { name: "Customers", path: "/customers", icon: Users },
    { name: "Repairs", path: "/repairs", icon: Wrench },
    { name: "Inventory", path: "/inventory", icon: Package },
    { name: "Invoices", path: "/invoices", icon: FileText },
    { name: "Reports", path: "/reports", icon: BarChart },
    { name: "Settings", path: "/settings", icon: Settings },
  ];

  return (
    <div
      className={cn(
        "fixed left-0 top-0 z-30 h-full bg-sidebar transition-all duration-300 border-r border-sidebar-border",
        isOpen ? "w-64" : "w-0 -translate-x-full md:w-16 md:translate-x-0"
      )}
    >
      <div className="flex h-16 items-center justify-between px-4">
        {isOpen && (
          <Link to="/" className="flex items-center space-x-2">
            <Wrench className="h-6 w-6 text-primary" />
            <span className="font-bold text-xl">RepairPro</span>
          </Link>
        )}
        <button
          onClick={toggleSidebar}
          className={cn(
            "rounded-md p-2 text-gray-400 hover:bg-gray-100",
            !isOpen && "hidden md:block"
          )}
        >
          {isOpen ? <X size={20} /> : <Menu size={20} />}
        </button>
      </div>

      <div className="flex flex-col space-y-1 px-2 py-4">
        {links.map((link) => (
          <Link
            key={link.path}
            to={link.path}
            className={cn(
              "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
              location.pathname === link.path
                ? "bg-primary text-primary-foreground"
                : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
              !isOpen && "justify-center px-0"
            )}
          >
            <link.icon className={cn("h-5 w-5", isOpen && "mr-2")} />
            {isOpen && <span>{link.name}</span>}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;
