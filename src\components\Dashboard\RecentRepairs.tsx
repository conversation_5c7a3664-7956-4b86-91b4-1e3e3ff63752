
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";
import RepairStatusBadge from "@/components/Repair/RepairStatusBadge";

interface RecentRepairsProps {
  recentRepairs: Array<{
    id: string;
    customerId: string;
    customerName: string;
    deviceType: string;
    brand: string;
    model: string;
    issueDescription: string;
    status: string;
    updatedAt: Date;
  }>;
  isLoading: boolean;
}

const RecentRepairs: React.FC<RecentRepairsProps> = ({ recentRepairs, isLoading }) => {

  if (isLoading) {
    return (
      <Card className="col-span-1 md:col-span-2">
        <CardHeader className="pb-2">
          <CardTitle>Recent Repairs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center justify-between border-b pb-3 last:border-0">
                <div className="flex flex-col space-y-2 w-2/3">
                  <div className="h-4 bg-muted rounded animate-pulse w-32"></div>
                  <div className="h-3 bg-muted rounded animate-pulse w-48"></div>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  <div className="h-5 bg-muted rounded animate-pulse w-20"></div>
                  <div className="h-3 bg-muted rounded animate-pulse w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!recentRepairs || recentRepairs.length === 0) {
    return (
      <Card className="col-span-1 md:col-span-2">
        <CardHeader className="pb-2">
          <CardTitle>Recent Repairs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <p className="text-muted-foreground">No recent repairs found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="col-span-1 md:col-span-2">
      <CardHeader className="pb-2">
        <CardTitle>Recent Repairs</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentRepairs.map((repair) => (
            <div
              key={repair.id}
              className="flex items-center justify-between border-b pb-3 last:border-0"
            >
              <div className="flex flex-col space-y-1">
                <span className="font-medium">{repair.customerName}</span>
                <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                  <span>{repair.brand} {repair.model}</span>
                  <span className="text-gray-400">|</span>
                  <span>{repair.issueDescription}</span>
                </div>
              </div>
              <div className="flex flex-col items-end space-y-1">
                <RepairStatusBadge status={repair.status} />
                <span className="text-xs text-muted-foreground">
                  {formatDistanceToNow(repair.updatedAt, { addSuffix: true })}
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentRepairs;
