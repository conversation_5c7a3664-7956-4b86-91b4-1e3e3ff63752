import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { cn } from "@/lib/utils"

const CustomerSearchDialog = DialogPrimitive.Root

const CustomerSearchDialogTrigger = DialogPrimitive.Trigger

const CustomerSearchDialogPortal = DialogPrimitive.Portal

const CustomerSearchDialogClose = DialogPrimitive.Close

const CustomerSearchDialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/20 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
CustomerSearchDialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const CustomerSearchDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <CustomerSearchDialogPortal>
    <CustomerSearchDialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[10%] z-50 grid w-[calc(100%-2rem)] max-w-md translate-x-[-50%] gap-4 border bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </CustomerSearchDialogPortal>
))
CustomerSearchDialogContent.displayName = DialogPrimitive.Content.displayName

export {
  CustomerSearchDialog,
  CustomerSearchDialogTrigger,
  CustomerSearchDialogContent,
  CustomerSearchDialogClose,
}
