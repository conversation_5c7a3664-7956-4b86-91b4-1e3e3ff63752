# Customer View Feature Documentation

## Overview

The Customer View feature allows users to view detailed information about a customer, including their contact information, repair history, and invoice history. This feature enhances the user experience by providing a comprehensive view of a customer's relationship with the repair shop and enables quick access to all customer-related data in one place.

## Components

### 1. CustomerDetail Component

The `CustomerDetail` component displays detailed information about a customer, including:

- Basic customer information (name, ID, contact details, address)
- Customer registration date
- Repair history with pagination
- Invoice history with pagination
- Customer deletion functionality

#### Key Features

- **Customer Information Section**: Displays the customer's name, ID, contact information, address, and registration date.
- **Tabbed Interface**: Organizes customer data into tabs for repairs and invoices.
- **Pagination**: Implements pagination for both repair and invoice history to handle customers with many records.
- **Delete Customer**: Provides the ability to delete a customer directly from the detail view.
- **Loading States**: Shows loading indicators while fetching data.
- **Error Handling**: Displays appropriate error messages if data fetching fails.

### 2. RepairCard Component

A sub-component used within CustomerDetail to display individual repair jobs in a consistent, user-friendly format.

#### Key Features

- **Status Badge**: Color-coded badge indicating the repair status.
- **Device Information**: Shows the device type, brand, and model.
- **Issue Description**: Brief description of the repair issue.
- **Cost Information**: Displays final cost for completed repairs or estimated cost for in-progress repairs.
- **Creation Date**: Shows when the repair was created.
- **View Details Button**: Allows navigation to the full repair details page.

### 3. InvoiceCard Component

A sub-component used within CustomerDetail to display individual invoices in a consistent, user-friendly format.

#### Key Features

- **Invoice Number**: Displays the invoice number prominently.
- **Status Badge**: Color-coded badge indicating the payment status (paid, partial, pending, overdue).
- **Related Repair**: Shows the associated repair job ID.
- **Financial Information**: Displays the total amount and remaining balance if applicable.
- **Creation Date**: Shows when the invoice was created.
- **View Details Button**: Allows navigation to the full invoice details page.

## Data Flow

1. User clicks the "View" button for a customer in the Customers page
2. The CustomerDetail component is displayed in a dialog
3. The component simultaneously:
   - Fetches repair jobs for the selected customer using the `getRepairJobsByCustomerId` function
   - Fetches invoices for the selected customer using the `getInvoices` function with a customer ID filter
4. Loading states are displayed while data is being fetched
5. Once data is loaded, repairs and invoices are displayed in separate tabs with pagination
6. User can:
   - Click on a repair to navigate to its detailed view
   - Click on an invoice to navigate to its detailed view
   - Delete the customer (with confirmation dialog)
   - Navigate between pages of repairs or invoices

## Implementation Details

### Database Integration

The feature integrates with the Supabase database to fetch customer, repair, and invoice data:

- Customer data is retrieved from the `customers` table
- Repair jobs are retrieved from the `repair_jobs` table filtered by `customer_id`
- Invoices are retrieved from the `invoices` table filtered by `customer_id`
- All tables use UUID primary keys for data integrity

### Pagination Implementation

The feature implements pagination for both repairs and invoices:

- Uses the reusable `usePagination` hook for consistent pagination behavior
- Configurable page size (default: 5 items per page)
- Includes page navigation controls (previous, next, page numbers)
- Maintains separate pagination state for repairs and invoices

### Error Handling

The implementation includes robust error handling:

- Separate loading states for repairs and invoices during data fetching
- Distinct error messages for repair and invoice data fetching failures
- Graceful handling of empty states when no repairs or invoices exist
- Error handling for customer deletion operations

### Customer Deletion

The feature includes customer deletion functionality:

- Confirmation dialog to prevent accidental deletions
- Loading state during deletion process
- Error handling for failed deletion attempts
- Automatic dialog closure and navigation after successful deletion

### Responsive Design and Overflow Handling

The implementation is fully responsive and properly handles content overflow:

- **Mobile-Friendly Layout**: Optimized for both mobile and desktop viewing
- **Adaptive Content**: Content adapts to different screen sizes with appropriate spacing and sizing
- **Dialog Responsiveness**: Dialogs adjust their size and padding based on screen size
- **Overflow Handling**: Dialog content has a maximum height with vertical scrolling
- **Text Handling**: Long text content is properly truncated or wrapped
- **Fixed Navigation**: Tab navigation remains visible while scrolling through content
- **Touch-Friendly**: Larger touch targets on mobile devices

### Navigation

The feature implements seamless navigation:

- Dialog-based view for customer details
- Direct navigation to repair details when a repair is selected
- Direct navigation to invoice details when an invoice is selected
- Dialog automatically closes when navigating to a repair or invoice
- Pagination navigation for browsing through multiple pages of data

## Code Structure

```
src/
├── components/
│   ├── Customer/
│   │   ├── CustomerDetail.tsx    # Main component for customer details view
│   │   └── CustomerForm.tsx      # Component for creating/editing customers
│   └── ui/
│       └── pagination.tsx        # Reusable pagination component
├── pages/
│   └── Customers.tsx             # Updated to include CustomerDetail dialog
├── services/
│   ├── customers.ts              # Customer data service with CRUD operations
│   ├── repairs.ts                # Repair service with getRepairJobsByCustomerId function
│   └── invoices.ts               # Invoice service with filtering by customer ID
├── hooks/
│   └── usePagination.ts          # Reusable pagination hook
└── lib/
    ├── currency.ts               # Utility for currency formatting
    └── data.ts                   # TypeScript interfaces and mock data
```

## Usage

To view a customer's details:

1. Navigate to the Customers page
2. Click the "View" (eye icon) button for any customer
3. The customer details dialog will open, showing their information
4. Use the tabs to switch between repairs and invoices
5. Browse through multiple pages using the pagination controls
6. Click "View Details" on any repair or invoice to navigate to its detailed page
7. To delete the customer, click the "Delete" button and confirm the action

## Future Enhancements

Potential future enhancements for this feature:

1. **Customer Notes**: Add ability to store and display notes about the customer
2. **Communication History**: Track and display communication history with the customer
3. **Device History**: Group repairs by device to show device history
4. **Export Functionality**: Allow exporting customer data, repair history, and invoice history
5. **Customer Preferences**: Store and display customer preferences (e.g., preferred contact method)
6. **Advanced Search and Filter**: Add ability to search and filter repair/invoice history by various criteria
7. **Print View**: Add a print-friendly view of customer details and history
8. **Customer Analytics**: Add visual charts showing repair frequency, spending patterns, and other metrics
9. **Bulk Operations**: Allow performing actions on multiple repairs or invoices at once
10. **Customer Loyalty Features**: Track customer loyalty metrics and display rewards or special offers
11. **Customer Portal Integration**: Prepare for future customer portal access with shareable links
12. **Document Storage**: Allow attaching and viewing documents related to the customer (ID proofs, etc.)
