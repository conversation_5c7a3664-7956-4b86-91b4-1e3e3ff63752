import React, { useState } from "react";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { InventoryItem } from "@/lib/data";
import { useAppContext } from "@/contexts/AppContext";
import { toast } from "@/components/ui/sonner";

interface InventoryFormProps {
  item?: InventoryItem;
  onSuccess: () => void;
  onCancel: () => void;
}

// Define schema for inventory item
const inventoryItemSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  category: z.string().min(1, { message: "Category is required" }),
  price: z.string()
    .min(1, { message: "Price is required" })
    .refine(val => !isNaN(parseFloat(val)), { message: "Must be a valid number" })
    .refine(val => parseFloat(val) >= 0, { message: "Must be a positive number" }),
  cost: z.string()
    .min(1, { message: "Cost is required" })
    .refine(val => !isNaN(parseFloat(val)), { message: "Must be a valid number" })
    .refine(val => parseFloat(val) >= 0, { message: "Must be a positive number" }),
  quantity: z.string()
    .min(1, { message: "Quantity is required" })
    .refine(val => !isNaN(parseInt(val)), { message: "Must be a valid number" })
    .refine(val => parseInt(val) >= 0, { message: "Must be a positive number" }),
  threshold: z.string()
    .min(1, { message: "Threshold is required" })
    .refine(val => !isNaN(parseInt(val)), { message: "Must be a valid number" })
    .refine(val => parseInt(val) >= 0, { message: "Must be a positive number" }),
  description: z.string().optional(),
});

type FormValues = z.infer<typeof inventoryItemSchema>;

const InventoryForm: React.FC<InventoryFormProps> = ({ item, onSuccess, onCancel }) => {
  const { addInventoryItem, updateInventoryItem } = useAppContext();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with default values
  const { register, handleSubmit, formState: { errors } } = useForm<FormValues>({
    resolver: zodResolver(inventoryItemSchema),
    defaultValues: item ? {
      name: item.name,
      category: item.category,
      price: item.price.toString(),
      cost: item.cost.toString(),
      quantity: item.quantity.toString(),
      threshold: item.threshold.toString(),
      description: item.description || "",
    } : {
      name: "",
      category: "",
      price: "",
      cost: "",
      quantity: "0",
      threshold: "5",
      description: "",
    }
  });

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      const itemData = {
        name: data.name,
        category: data.category,
        price: parseFloat(data.price),
        cost: parseFloat(data.cost),
        quantity: parseInt(data.quantity),
        threshold: parseInt(data.threshold),
        description: data.description,
      };

      if (item) {
        // Update existing item
        await updateInventoryItem({
          id: item.id,
          ...itemData
        });
        toast.success("Inventory item updated successfully");
      } else {
        // Create new item
        await addInventoryItem(itemData);
        toast.success("Inventory item added successfully");
      }
      
      onSuccess();
    } catch (error) {
      console.error("Error saving inventory item:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save inventory item");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Item Name</Label>
          <Input
            id="name"
            placeholder="Enter item name"
            {...register("name")}
          />
          {errors.name && (
            <p className="text-sm text-red-500">{errors.name.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Input
            id="category"
            placeholder="Enter category"
            {...register("category")}
          />
          {errors.category && (
            <p className="text-sm text-red-500">{errors.category.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="price">Selling Price</Label>
          <Input
            id="price"
            type="number"
            min="0"
            step="0.01"
            placeholder="0.00"
            {...register("price")}
          />
          {errors.price && (
            <p className="text-sm text-red-500">{errors.price.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="cost">Cost Price</Label>
          <Input
            id="cost"
            type="number"
            min="0"
            step="0.01"
            placeholder="0.00"
            {...register("cost")}
          />
          {errors.cost && (
            <p className="text-sm text-red-500">{errors.cost.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="quantity">Quantity in Stock</Label>
          <Input
            id="quantity"
            type="number"
            min="0"
            step="1"
            placeholder="0"
            {...register("quantity")}
          />
          {errors.quantity && (
            <p className="text-sm text-red-500">{errors.quantity.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="threshold">Low Stock Threshold</Label>
          <Input
            id="threshold"
            type="number"
            min="0"
            step="1"
            placeholder="5"
            {...register("threshold")}
          />
          {errors.threshold && (
            <p className="text-sm text-red-500">{errors.threshold.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          placeholder="Enter item description (optional)"
          className="min-h-[100px]"
          {...register("description")}
        />
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isSubmitting ? "Saving..." : item ? "Update Item" : "Add Item"}
        </Button>
      </div>
    </form>
  );
};

export default InventoryForm;
