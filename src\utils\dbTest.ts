import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

/**
 * Tests the database connection and verifies if data exists
 * @returns Promise<boolean> - True if connection is successful and data exists
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    // Test customers table
    const { data: customers, error: customersError } = await supabase
      .from('customers')
      .select('count')
      .limit(1)
      .single();
    
    if (customersError) {
      console.error("Error connecting to customers table:", customersError);
      toast.error(`Database connection error: ${customersError.message}`);
      return false;
    }
    
    // Test repair_jobs table
    const { data: repairs, error: repairsError } = await supabase
      .from('repair_jobs')
      .select('count')
      .limit(1)
      .single();
    
    if (repairsError) {
      console.error("Error connecting to repair_jobs table:", repairsError);
      toast.error(`Database connection error: ${repairsError.message}`);
      return false;
    }
    
    // Test invoices table
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('count')
      .limit(1)
      .single();
    
    if (invoicesError) {
      console.error("Error connecting to invoices table:", invoicesError);
      toast.error(`Database connection error: ${invoicesError.message}`);
      return false;
    }
    
    // All tests passed
    toast.success("Database connection successful!");
    return true;
  } catch (error) {
    console.error("Unexpected error testing database connection:", error);
    toast.error("Failed to connect to database");
    return false;
  }
}

/**
 * Verifies if a specific customer exists in the database
 * @param id Customer ID to check
 * @returns Promise<boolean> - True if customer exists
 */
export async function verifyCustomerExists(id: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('customers')
      .select('id')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No customer found
        toast.error(`Customer with ID ${id} not found in database`);
        return false;
      }
      
      console.error("Error verifying customer:", error);
      toast.error(`Database error: ${error.message}`);
      return false;
    }
    
    if (data) {
      toast.success(`Customer with ID ${id} exists in database`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error("Unexpected error verifying customer:", error);
    toast.error("Failed to verify customer");
    return false;
  }
}

/**
 * Verifies if a specific invoice exists in the database
 * @param id Invoice ID to check
 * @returns Promise<boolean> - True if invoice exists
 */
export async function verifyInvoiceExists(id: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('invoices')
      .select('id')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        // No invoice found
        toast.error(`Invoice with ID ${id} not found in database`);
        return false;
      }
      
      console.error("Error verifying invoice:", error);
      toast.error(`Database error: ${error.message}`);
      return false;
    }
    
    if (data) {
      toast.success(`Invoice with ID ${id} exists in database`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error("Unexpected error verifying invoice:", error);
    toast.error("Failed to verify invoice");
    return false;
  }
}
