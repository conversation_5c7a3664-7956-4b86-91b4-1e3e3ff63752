import React, { forwardRef } from "react";
import { QRCodeSVG } from "qrcode.react";
import { RepairJob } from "@/lib/data";

interface RepairTicketProps {
  repair: RepairJob;
  shopName?: string;
  shopPhone?: string;
}

// This component is designed to be printed on a thermal printer
// Standard thermal receipt printers are typically 58mm or 80mm wide
const RepairTicket = forwardRef<HTMLDivElement, RepairTicketProps>(
  ({ repair, shopName = "Mobile Repair Shop", shopPhone = "+91 98765 43210" }, ref) => {
    // Format date to a more readable format
    const formattedDate = repair.createdAt.toLocaleDateString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });

    // Create a URL for the QR code that points to the repair details
    // In a real app, this would be a full URL to the repair details page
    const qrCodeValue = `repair:${repair.id}`;

    return (
      <div
        ref={ref}
        className="repair-ticket bg-white p-2"
        style={{
          width: "76mm", // Standard 80mm thermal printer (with margins)
          fontFamily: "monospace",
          fontSize: "10px",
          lineHeight: "1.2",
          minHeight: "200px", // Ensure the ticket has a minimum height
          display: "block", // Ensure the element is displayed as a block
        }}
      >
        {/* Shop Header */}
        <div className="text-center font-bold mb-2" style={{ fontSize: "12px" }}>
          {shopName}
        </div>
        <div className="text-center mb-2">{shopPhone}</div>

        {/* Repair ID and Date */}
        <div className="flex justify-between border-t border-b py-1 mb-2">
          <div className="font-bold">ID: {repair.id}</div>
          <div>{formattedDate}</div>
        </div>

        {/* Customer Info */}
        <div className="mb-2">
          <div className="font-bold">Customer:</div>
          <div>{repair.customerName}</div>
        </div>

        {/* Device Info */}
        <div className="mb-2">
          <div className="font-bold">Device:</div>
          <div>{repair.deviceType} - {repair.brand} {repair.model}</div>
          {repair.serialNumber && <div>S/N: {repair.serialNumber}</div>}
        </div>

        {/* Issue */}
        <div className="mb-2">
          <div className="font-bold">Issue:</div>
          <div className="text-wrap">{repair.issueDescription.substring(0, 100)}{repair.issueDescription.length > 100 ? "..." : ""}</div>
        </div>

        {/* Status */}
        <div className="mb-2">
          <div className="font-bold">Status:</div>
          <div className="uppercase">{repair.status}</div>
        </div>

        {/* QR Code */}
        <div className="flex justify-center my-2">
          <QRCodeSVG value={qrCodeValue} size={100} />
        </div>

        {/* Footer */}
        <div className="text-center text-xs mt-2 pt-1 border-t">
          <div>Thank you for your business!</div>
          <div>Scan QR code for repair status</div>
        </div>
      </div>
    );
  }
);

RepairTicket.displayName = "RepairTicket";

export default RepairTicket;