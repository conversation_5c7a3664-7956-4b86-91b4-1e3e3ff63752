
import React, { useState, useEffect } from "react";
import Header from "@/components/Layout/Header";
import Sidebar from "@/components/Layout/Sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { useAppContext } from "@/contexts/AppContext";
import { formatINR } from "@/lib/currency";
import { Eye, Search } from "lucide-react";
import InvoiceDetail from "@/components/Invoice/InvoiceDetail";
import { Invoice } from "@/lib/data";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

import { Label } from "@/components/ui/label";
import { useParams } from "react-router-dom";
import { usePagination } from "@/hooks/usePagination";
import { useSorting } from "@/hooks/useSorting";
import { SortableHeader } from "@/components/ui/sortable-header";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

const Invoices = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const { invoices, refreshInvoices } = useAppContext();
  const { id } = useParams<{ id: string }>();

  // Filtering state
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFrom, setDateFrom] = useState<Date | undefined>(undefined);
  const [dateTo, setDateTo] = useState<Date | undefined>(undefined);

  // Pagination constants
  const PAGE_SIZE = 10;

  // Filtered invoices
  const filteredInvoices = invoices.filter(invoice => {
    // Search term filter
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = searchTerm === "" ||
      invoice.customerName.toLowerCase().includes(searchLower) ||
      invoice.id.toLowerCase().includes(searchLower) ||
      invoice.invoiceNumber?.toLowerCase().includes(searchLower) ||
      invoice.repairJobId.toLowerCase().includes(searchLower);

    // Status filter
    const matchesStatus = statusFilter === "all" || invoice.paymentStatus === statusFilter;

    // Date range filter
    const createdDate = new Date(invoice.createdAt);
    const matchesDateFrom = !dateFrom || createdDate >= dateFrom;
    const matchesDateTo = !dateTo || createdDate <= dateTo;

    return matchesSearch && matchesStatus && matchesDateFrom && matchesDateTo;
  });

  // Sorting
  const {
    items: sortedInvoices,
    sortConfig,
    requestSort
  } = useSorting<Invoice>(filteredInvoices, 'createdAt', 'desc');

  // Refresh invoices when component mounts
  useEffect(() => {
    refreshInvoices();
  }, []);

  // Handle invoice ID from URL
  useEffect(() => {
    if (id && invoices.length > 0) {
      const invoice = invoices.find(inv => inv.id === id);
      if (invoice) {
        setSelectedInvoice(invoice);
      }
    }
  }, [id, invoices]);

  // Pagination
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedInvoices,
    goToPage,
    totalItems
  } = usePagination({
    data: sortedInvoices,
    pageSize: PAGE_SIZE,
    initialPage: 1,
  });

  // Column definitions for the table
  const columns = [
    { key: 'invoiceNumber' as keyof Invoice, label: 'Invoice #' },
    { key: 'customerName' as keyof Invoice, label: 'Customer' },
    { key: 'repairJobId' as keyof Invoice, label: 'Repair ID' },
    { key: 'total' as keyof Invoice, label: 'Total', className: 'text-right' },
    { key: 'paidAmount' as keyof Invoice, label: 'Paid', className: 'text-right' },
    { key: 'balance' as keyof Invoice, label: 'Balance', className: 'text-right' },
    { key: 'paymentStatus' as keyof Invoice, label: 'Status', className: 'text-center' },
    { key: 'createdAt' as keyof Invoice, label: 'Date' },
  ];

  return (
    <div className="flex min-h-screen">
      <Sidebar isOpen={sidebarOpen} toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? "md:ml-64" : "md:ml-16"}`}>
        <Header toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isOpen={sidebarOpen} />

        <main className="px-4 py-6 md:px-6">
          {/* Financial Summary Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-blue-800">Total Value</span>
                  <span className="text-2xl font-bold text-blue-900">
                    {formatINR(invoices.reduce((sum, invoice) => sum + invoice.total, 0))}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-4">
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-green-800">Collected</span>
                  <span className="text-2xl font-bold text-green-900">
                    {formatINR(invoices.reduce((sum, invoice) => sum + invoice.paidAmount, 0))}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-amber-50 border-amber-200">
              <CardContent className="p-4">
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-amber-800">Outstanding</span>
                  <span className="text-2xl font-bold text-amber-900">
                    {formatINR(invoices.reduce((sum, invoice) => sum + invoice.balance, 0))}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-red-50 border-red-200">
              <CardContent className="p-4">
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-red-800">Overdue</span>
                  <span className="text-2xl font-bold text-red-900">
                    {invoices.filter(invoice => invoice.paymentStatus === 'overdue').length}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Invoices</h1>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="relative w-full">
                  <Label htmlFor="search" className="mb-1.5 block">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="Search invoices..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="status" className="mb-1.5 block">Status</Label>
                    <Select
                      value={statusFilter}
                      onValueChange={setStatusFilter}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                        <SelectItem value="partial">Partial</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="overdue">Overdue</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="dateFrom" className="mb-1.5 block">From Date</Label>
                    <Input
                      id="dateFrom"
                      type="date"
                      onChange={(e) => setDateFrom(e.target.value ? new Date(e.target.value) : undefined)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="dateTo" className="mb-1.5 block">To Date</Label>
                    <Input
                      id="dateTo"
                      type="date"
                      onChange={(e) => setDateTo(e.target.value ? new Date(e.target.value) : undefined)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>All Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Desktop view - Table */}
              <div className="hidden md:block overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      {columns.map(column => (
                        <SortableHeader
                          key={String(column.key)}
                          column={column}
                          sortConfig={sortConfig}
                          onSort={requestSort}
                        />
                      ))}
                      <th className="py-3 px-4 text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedInvoices.length === 0 ? (
                      <tr>
                        <td colSpan={9} className="text-center py-8">
                          No invoices found.
                        </td>
                      </tr>
                    ) : (
                      paginatedInvoices.map(invoice => (
                        <tr key={invoice.id} className="border-b hover:bg-muted/50">
                          <td className="py-3 px-4">{invoice.invoiceNumber || invoice.id}</td>
                          <td className="py-3 px-4 font-medium">{invoice.customerName}</td>
                          <td className="py-3 px-4">{invoice.repairJobId}</td>
                          <td className="py-3 px-4 text-right">{formatINR(invoice.total)}</td>
                          <td className="py-3 px-4 text-right">{formatINR(invoice.paidAmount)}</td>
                          <td className="py-3 px-4 text-right">{formatINR(invoice.balance)}</td>
                          <td className="py-3 px-4 text-center">
                            <Badge
                              variant={invoice.paymentStatus === "paid" ? "default" :
                                      invoice.paymentStatus === "partial" ? "secondary" :
                                      invoice.paymentStatus === "overdue" ? "destructive" : "outline"}
                              className={invoice.paymentStatus === "paid" ? "bg-green-500 hover:bg-green-500/80" :
                                        invoice.paymentStatus === "partial" ? "bg-amber-500 hover:bg-amber-500/80" :
                                        undefined}
                            >
                              {invoice.paymentStatus === "paid"
                                ? "Paid"
                                : invoice.paymentStatus === "partial"
                                ? "Partial"
                                : invoice.paymentStatus === "overdue"
                                ? "Overdue"
                                : "Pending"}
                            </Badge>
                          </td>
                          <td className="py-3 px-4">
                            {invoice.createdAt.toLocaleDateString('en-IN')}
                          </td>
                          <td className="py-3 px-4 text-center">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedInvoice(invoice)}
                              className="h-8 px-2 flex items-center gap-1"
                            >
                              <Eye className="h-4 w-4" />
                              <span>View</span>
                            </Button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* Mobile view - Cards */}
              <div className="md:hidden space-y-4">
                {filteredInvoices.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No invoices found.
                  </div>
                ) : (
                  paginatedInvoices.map(invoice => (
                    <div key={invoice.id} className="border rounded-lg p-4 hover:bg-muted/50">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium">{invoice.invoiceNumber || invoice.id}</h3>
                          <p className="text-xs text-muted-foreground">
                            {invoice.createdAt.toLocaleDateString('en-IN')}
                          </p>
                        </div>
                        <Badge
                          variant={invoice.paymentStatus === "paid" ? "default" :
                                  invoice.paymentStatus === "partial" ? "secondary" :
                                  invoice.paymentStatus === "overdue" ? "destructive" : "outline"}
                          className={invoice.paymentStatus === "paid" ? "bg-green-500 hover:bg-green-500/80" :
                                    invoice.paymentStatus === "partial" ? "bg-amber-500 hover:bg-amber-500/80" :
                                    undefined}
                        >
                          {invoice.paymentStatus === "paid"
                            ? "Paid"
                            : invoice.paymentStatus === "partial"
                            ? "Partial"
                            : invoice.paymentStatus === "overdue"
                            ? "Overdue"
                            : "Pending"}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-1 text-sm mb-3">
                        <div>
                          <span className="text-muted-foreground">Customer:</span> {invoice.customerName}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Repair ID:</span> {invoice.repairJobId}
                        </div>
                      </div>

                      <div className="flex justify-between items-center border-t pt-2">
                        <div className="grid grid-cols-1 gap-1 text-sm">
                          <div>
                            <span className="text-muted-foreground">Total:</span> {formatINR(invoice.total)}
                          </div>
                          <div>
                            <span className="text-muted-foreground">Balance:</span> {formatINR(invoice.balance)}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedInvoice(invoice)}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4">
                  <p className="text-sm text-muted-foreground">
                    Showing {Math.min((currentPage - 1) * PAGE_SIZE + 1, totalItems)} to {Math.min(currentPage * PAGE_SIZE, totalItems)} of {totalItems} invoices
                  </p>

                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            if (currentPage > 1) goToPage(currentPage - 1);
                          }}
                          aria-disabled={currentPage === 1}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>

                      {Array.from({ length: totalPages }).map((_, i) => {
                        const page = i + 1;
                        // Show first page, last page, and pages around current page
                        if (
                          page === 1 ||
                          page === totalPages ||
                          (page >= currentPage - 1 && page <= currentPage + 1)
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  goToPage(page);
                                }}
                                isActive={page === currentPage}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        }

                        // Show ellipsis
                        if (
                          (page === 2 && currentPage > 3) ||
                          (page === totalPages - 1 && currentPage < totalPages - 2)
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationEllipsis />
                            </PaginationItem>
                          );
                        }

                        return null;
                      })}

                      <PaginationItem>
                        <PaginationNext
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            if (currentPage < totalPages) goToPage(currentPage + 1);
                          }}
                          aria-disabled={currentPage === totalPages}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </div>

      {/* Invoice Detail Dialog */}
      <Dialog open={selectedInvoice !== null} onOpenChange={(open) => !open && setSelectedInvoice(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Invoice Details</DialogTitle>
          </DialogHeader>
          {selectedInvoice && <InvoiceDetail invoice={selectedInvoice} onRefresh={refreshInvoices} />}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Invoices;
