import React from "react";
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { InventoryItem } from "@/lib/data";
import InventoryForm from "./InventoryForm";

interface InventoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item?: InventoryItem;
  onSuccess: () => void;
}

const InventoryDialog: React.FC<InventoryDialogProps> = ({
  open,
  onOpenChange,
  item,
  onSuccess,
}) => {
  const handleSuccess = () => {
    onSuccess();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{item ? "Edit Inventory Item" : "Add New Inventory Item"}</DialogTitle>
        </DialogHeader>
        <InventoryForm
          item={item}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
};

export default InventoryDialog;
