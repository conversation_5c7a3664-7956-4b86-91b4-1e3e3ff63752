import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search, Plus, Minus } from "lucide-react";
import { useAppContext } from "@/contexts/AppContext";
import { InventoryItem } from "@/lib/data";
import { formatINR } from "@/lib/currency";
import { ScrollArea } from "@/components/ui/scroll-area";

interface InventoryItemSelectorProps {
  onItemsSelected: (items: Array<{ id: string; name: string; quantity: number; cost: number }>) => void;
  initialItems?: Array<{ id: string; name: string; quantity: number; cost: number }>;
  disabled?: boolean;
}

const InventoryItemSelector: React.FC<InventoryItemSelectorProps> = ({
  onItemsSelected,
  initialItems = [],
  disabled = false
}) => {
  const { inventory, refreshInventory } = useAppContext();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<
    Array<{ id: string; name: string; quantity: number; cost: number }>
  >(initialItems);

  // Filter inventory based on search query
  const filteredInventory = inventory.filter((item) => {
    if (!searchQuery.trim()) return true;
    const query = searchQuery.toLowerCase();
    return (
      item.name.toLowerCase().includes(query) ||
      item.category.toLowerCase().includes(query)
    );
  });

  // Update parent component when selected items change
  useEffect(() => {
    onItemsSelected(selectedItems);
  }, [selectedItems, onItemsSelected]);

  // Add an item to the selected items
  const addItem = (item: InventoryItem) => {
    // Check if item is already selected
    const existingItemIndex = selectedItems.findIndex((i) => i.id === item.id);

    if (existingItemIndex >= 0) {
      // Increment quantity if already selected
      const updatedItems = [...selectedItems];
      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantity: updatedItems[existingItemIndex].quantity + 1
      };
      setSelectedItems(updatedItems);
    } else {
      // Add new item with quantity 1
      setSelectedItems([
        ...selectedItems,
        {
          id: item.id,
          name: item.name,
          quantity: 1,
          cost: item.price
        }
      ]);
    }
  };

  // Remove an item from the selected items
  const removeItem = (itemId: string) => {
    const existingItemIndex = selectedItems.findIndex((i) => i.id === itemId);

    if (existingItemIndex >= 0) {
      const updatedItems = [...selectedItems];
      if (updatedItems[existingItemIndex].quantity > 1) {
        // Decrement quantity if more than 1
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity - 1
        };
        setSelectedItems(updatedItems);
      } else {
        // Remove item if quantity is 1
        setSelectedItems(selectedItems.filter((i) => i.id !== itemId));
      }
    }
  };

  // Update quantity directly
  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      setSelectedItems(selectedItems.filter((i) => i.id !== itemId));
      return;
    }

    const updatedItems = selectedItems.map((item) =>
      item.id === itemId ? { ...item, quantity } : item
    );

    setSelectedItems(updatedItems);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Add Parts from Inventory</h3>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => refreshInventory()}
          disabled={disabled}
        >
          Refresh Inventory
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search inventory..."
          className="pl-8"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          disabled={disabled}
        />
      </div>

      <div className="border rounded-md">
        <ScrollArea className="h-[200px]">
          <div className="p-4 space-y-2">
            {filteredInventory.length === 0 ? (
              <p className="text-center text-muted-foreground py-4">
                No inventory items found.
              </p>
            ) : (
              filteredInventory.map((item) => (
                <div
                  key={item.id}
                  className="flex justify-between items-center p-2 hover:bg-muted/50 rounded-md"
                >
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {item.category} • {formatINR(item.price)} • {item.quantity} in stock
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => addItem(item)}
                    disabled={disabled || item.quantity <= 0}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

      {selectedItems.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Selected Parts</h4>
          <div className="border rounded-md p-4 space-y-2">
            {selectedItems.map((item) => (
              <div
                key={item.id}
                className="flex justify-between items-center p-2 hover:bg-muted/50 rounded-md"
              >
                <div className="flex-1">
                  <p className="font-medium">{item.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatINR(item.cost)} each
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeItem(item.id)}
                    disabled={disabled}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <Input
                    type="number"
                    min="1"
                    value={item.quantity}
                    onChange={(e) => updateQuantity(item.id, parseInt(e.target.value) || 0)}
                    className="w-16 h-8 text-center"
                    disabled={disabled}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => addItem(inventory.find(i => i.id === item.id)!)}
                    disabled={disabled}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryItemSelector;
