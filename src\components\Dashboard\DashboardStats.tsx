
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Smartphone, Clock, CheckCircle, Package, DollarSign } from "lucide-react";
import { formatINR } from "@/lib/currency";

interface DashboardStatsProps {
  statusCounts: {
    pending: number;
    inProgress: number;
    completed: number;
    delivered: number;
    cancelled: number;
    on_hold: number;
  };
  revenueSummary: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    pending: number;
  };
  lowStockItems: any[];
  isLoading: boolean;
}

const DashboardStats: React.FC<DashboardStatsProps> = ({
  statusCounts,
  revenueSummary: revenue,
  lowStockItems,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="stats-card">
            <CardContent className="p-4 flex-1">
              <div className="flex justify-between items-start">
                <div>
                  <div className="h-4 bg-muted rounded animate-pulse w-24 mb-2"></div>
                  <div className="h-8 bg-muted rounded animate-pulse w-20"></div>
                </div>
                <div className="rounded-full bg-muted p-2 animate-pulse"></div>
              </div>
              <div className="mt-2 flex">
                <div className="h-4 bg-muted rounded animate-pulse w-32"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="stats-card">
        <CardContent className="p-4 flex-1">
          <div className="flex justify-between items-start">
            <div>
              <p className="stats-label">Total Active Repairs</p>
              <p className="stats-value">{statusCounts.pending + statusCounts.inProgress + statusCounts.on_hold}</p>
            </div>
            <div className="rounded-full bg-blue-100 p-2">
              <Smartphone className="h-5 w-5 text-primary" />
            </div>
          </div>
          <div className="mt-2 flex flex-wrap text-sm">
            <div className="mr-4">
              <span className="text-gray-500">Pending: </span>
              <span className="font-medium">{statusCounts.pending}</span>
            </div>
            <div className="mr-4">
              <span className="text-gray-500">In Progress: </span>
              <span className="font-medium">{statusCounts.inProgress}</span>
            </div>
            <div>
              <span className="text-gray-500">On Hold: </span>
              <span className="font-medium">{statusCounts.on_hold}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="stats-card">
        <CardContent className="p-4 flex-1">
          <div className="flex justify-between items-start">
            <div>
              <p className="stats-label">Completed Repairs</p>
              <p className="stats-value">{statusCounts.completed + statusCounts.delivered}</p>
            </div>
            <div className="rounded-full bg-green-100 p-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
          </div>
          <div className="mt-2 flex text-sm">
            <div className="mr-4">
              <span className="text-gray-500">Ready: </span>
              <span className="font-medium">{statusCounts.completed}</span>
            </div>
            <div>
              <span className="text-gray-500">Delivered: </span>
              <span className="font-medium">{statusCounts.delivered}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="stats-card">
        <CardContent className="p-4 flex-1">
          <div className="flex justify-between items-start">
            <div>
              <p className="stats-label">Low Stock Items</p>
              <p className="stats-value">{lowStockItems.length}</p>
            </div>
            <div className="rounded-full bg-amber-100 p-2">
              <Package className="h-5 w-5 text-amber-600" />
            </div>
          </div>
          <div className="mt-2 text-sm">
            <span className="text-gray-500">Categories: </span>
            <span className="font-medium">
              {Array.from(new Set(lowStockItems.map(item => item.category))).join(", ")}
            </span>
          </div>
        </CardContent>
      </Card>

      <Card className="stats-card">
        <CardContent className="p-4 flex-1">
          <div className="flex justify-between items-start">
            <div>
              <p className="stats-label">Revenue This Month</p>
              <p className="stats-value">{formatINR(revenue.thisMonth)}</p>
            </div>
            <div className="rounded-full bg-green-100 p-2">
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
          </div>
          <div className="mt-2 text-sm">
            <span className="text-gray-500">Pending Payments: </span>
            <span className="font-medium">{formatINR(revenue.pending)}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardStats;
