
import { supabase } from "@/integrations/supabase/client";
import { RepairJob, RepairStatus, InventoryItem } from "@/lib/data";
import { getInventoryItemById, updateInventoryItem } from "./inventory";

/**
 * Generate a consistent short ID from a database ID
 * This ensures the same database ID always maps to the same short ID
 * @param dbId The database ID
 * @returns A short ID in the format R###
 */
export function generateShortId(dbId: string): string {
  // Use the last 3 characters of the database ID to generate a number
  // This ensures the same database ID always generates the same short ID
  const hash = dbId.split('').reduce((acc, char) => {
    return (acc * 31 + char.charCodeAt(0)) % 900;
  }, 0);

  // Generate a number between 100 and 999
  const shortId = 100 + Math.abs(hash);

  return `R${shortId}`;
}

export async function getRepairJobs(): Promise<RepairJob[]> {
  try {
    const { data, error } = await supabase
      .from('repair_jobs')
      .select(`
        *,
        customers(name)
      `);

    if (error) {
      console.error("Error fetching repair jobs from API:", error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.log("No repair jobs found in API");
      return [];
    }

    return data.map(item => {
      // Try to parse parts_used if it exists
      let partsUsed: Array<{ id: string; name: string; cost: number; quantity: number }> = [];
      const itemWithPartsUsed = item as any;

      if (itemWithPartsUsed.parts_used) {
        try {
          // Parse the JSONB data
          const parsedParts = typeof itemWithPartsUsed.parts_used === 'string'
            ? JSON.parse(itemWithPartsUsed.parts_used)
            : itemWithPartsUsed.parts_used;

          if (Array.isArray(parsedParts)) {
            partsUsed = parsedParts;
          }
        } catch (error) {
          console.error("Error parsing parts_used JSON:", error);
        }
      }

      return {
        id: generateShortId(item.id),
        dbId: item.id,
        customerId: item.customer_id,
        customerName: item.customers.name,
        deviceType: item.device_type,
        brand: item.brand,
        model: item.model,
        serialNumber: item.serial_number || undefined,
        issueDescription: item.issue_description,
        technicianId: item.technician_id || undefined,
        technicianName: item.technician_name || undefined,
        status: item.status as RepairStatus,
        estimatedCost: item.estimated_cost || undefined,
        finalCost: item.final_cost || undefined,
        partsUsed: partsUsed,
        notes: item.notes || undefined,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
        completedAt: item.completed_at ? new Date(item.completed_at) : undefined,
        deliveredAt: item.delivered_at ? new Date(item.delivered_at) : undefined
      };
    });
  } catch (error) {
    console.error("Error in getRepairJobs:", error);
    throw error;
  }
}

export async function getRepairJobById(id: string): Promise<RepairJob | null> {
  try {
    // Fetch from the API
    const { data, error } = await supabase
      .from('repair_jobs')
      .select(`
        *,
        customers(name)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        console.log("No repair job found in API");
        return null; // No repair job found
      }
      console.error("Error fetching repair job from API:", error);
      return null;
    }

    // Try to get parts from parts_used table first
    let partsUsed: Array<{ id: string; name: string; cost: number; quantity: number }> = [];

    // First check if we have parts in the parts_used field (JSONB)
    // Use type assertion to handle the dynamic field
    const dataWithPartsUsed = data as any;
    if (dataWithPartsUsed.parts_used) {
      try {
        // Parse the JSONB data
        const parsedParts = typeof dataWithPartsUsed.parts_used === 'string'
          ? JSON.parse(dataWithPartsUsed.parts_used)
          : dataWithPartsUsed.parts_used;

        if (Array.isArray(parsedParts)) {
          partsUsed = parsedParts;
        }
      } catch (error) {
        console.error("Error parsing parts_used JSON:", error);
      }
    }

    // If no parts found in JSONB, try the parts_used table
    if (partsUsed.length === 0) {
      const { data: partsData, error: partsError } = await supabase
        .from('parts_used')
        .select(`
          *,
          inventory_items(id, name)
        `)
        .eq('repair_job_id', id);

      if (partsError) {
        console.error("Error fetching parts used:", partsError);
      } else if (partsData && partsData.length > 0) {
        partsUsed = partsData.map(part => ({
          id: part.inventory_items.id,
          name: part.inventory_items.name,
          cost: part.cost,
          quantity: part.quantity
        }));
      }
    }

    const repairJob = {
      id: generateShortId(data.id),
      dbId: data.id,
      customerId: data.customer_id,
      customerName: data.customers.name,
      deviceType: data.device_type,
      brand: data.brand,
      model: data.model,
      serialNumber: data.serial_number || undefined,
      issueDescription: data.issue_description,
      technicianId: data.technician_id || undefined,
      technicianName: data.technician_name || undefined,
      status: data.status as RepairStatus,
      estimatedCost: data.estimated_cost || undefined,
      finalCost: data.final_cost || undefined,
      partsUsed: partsUsed,
      notes: data.notes || undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      completedAt: data.completed_at ? new Date(data.completed_at) : undefined,
      deliveredAt: data.delivered_at ? new Date(data.delivered_at) : undefined
    };

    return repairJob;
  } catch (error) {
    console.error("Error in getRepairJobById:", error);
    return null;
  }
}

export async function createRepairJob(repair: Omit<RepairJob, "id" | "createdAt" | "updatedAt" | "partsUsed">): Promise<RepairJob> {
  try {
    // Prepare the insert data
    const insertData: any = {
      customer_id: repair.customerId,
      device_type: repair.deviceType,
      brand: repair.brand,
      model: repair.model,
      serial_number: repair.serialNumber,
      issue_description: repair.issueDescription,
      technician_id: repair.technicianId,
      technician_name: repair.technicianName,
      status: repair.status,
      estimated_cost: repair.estimatedCost,
      final_cost: repair.finalCost,
      completed_at: repair.completedAt?.toISOString(),
      delivered_at: repair.deliveredAt?.toISOString()
    };

    // Handle notes field - ensure it's an array for PostgreSQL
    if (repair.notes) {
      if (Array.isArray(repair.notes)) {
        insertData.notes = repair.notes;
      } else if (typeof repair.notes === 'string') {
        insertData.notes = [repair.notes];
      }
    } else {
      insertData.notes = [];
    }

    const { data, error } = await supabase
      .from('repair_jobs')
      .insert(insertData)
      .select(`
        *,
        customers(name)
      `)
      .single();

    if (error) {
      console.error("Error creating repair job:", error);
      throw error;
    }

    return {
      id: generateShortId(data.id),
      dbId: data.id,
      customerId: data.customer_id,
      customerName: data.customers.name,
      deviceType: data.device_type,
      brand: data.brand,
      model: data.model,
      serialNumber: data.serial_number || undefined,
      issueDescription: data.issue_description,
      technicianId: data.technician_id || undefined,
      technicianName: data.technician_name || undefined,
      status: data.status as RepairStatus,
      estimatedCost: data.estimated_cost || undefined,
      finalCost: data.final_cost || undefined,
      partsUsed: [],
      notes: data.notes || undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      completedAt: data.completed_at ? new Date(data.completed_at) : undefined,
      deliveredAt: data.delivered_at ? new Date(data.delivered_at) : undefined
    };
  } catch (error) {
    console.error("Error in createRepairJob:", error);
    throw error;
  }
}

export async function updateRepairJob(repair: RepairJob): Promise<RepairJob> {
  try {
    // Get the existing repair job to compare parts used
    let existingRepair: RepairJob | null = null;
    if (repair.dbId || repair.id) {
      try {
        existingRepair = await getRepairJobById(repair.dbId || repair.id);
      } catch (error) {
        console.warn("Could not fetch existing repair job for parts comparison:", error);
      }
    }

    // Prepare the update data
    const updateData: any = {
      customer_id: repair.customerId,
      device_type: repair.deviceType,
      brand: repair.brand,
      model: repair.model,
      serial_number: repair.serialNumber,
      issue_description: repair.issueDescription,
      technician_id: repair.technicianId,
      technician_name: repair.technicianName,
      status: repair.status,
      estimated_cost: repair.estimatedCost,
      final_cost: repair.finalCost,
      updated_at: new Date().toISOString(),
      completed_at: repair.completedAt?.toISOString(),
      delivered_at: repair.deliveredAt?.toISOString()
    };

    // Handle notes field - ensure it's an array for PostgreSQL
    if (repair.notes) {
      if (Array.isArray(repair.notes)) {
        updateData.notes = repair.notes;
      } else if (typeof repair.notes === 'string') {
        updateData.notes = [repair.notes];
      }
    } else {
      updateData.notes = [];
    }

    // Handle parts_used field - store as JSONB in PostgreSQL
    if (repair.partsUsed && repair.partsUsed.length > 0) {
      updateData.parts_used = JSON.stringify(repair.partsUsed);
    } else {
      updateData.parts_used = null;
    }

    // Update in the API
    const { data, error } = await supabase
      .from('repair_jobs')
      .update(updateData)
      .eq('id', repair.dbId || repair.id)
      .select(`
        *,
        customers(name)
      `)
      .single();

    if (error) {
      console.error("Error updating repair job in API:", error);
      // API failed, throw the error
      throw error;
    }

    // Update parts used in the database
    if (repair.partsUsed && repair.partsUsed.length > 0) {
      // First, delete existing parts_used entries for this repair
      const { error: deleteError } = await supabase
        .from('parts_used')
        .delete()
        .eq('repair_job_id', data.id);

      if (deleteError) {
        console.error("Error deleting existing parts used:", deleteError);
      }

      // Then, insert new parts_used entries
      for (const part of repair.partsUsed) {
        const { error: insertError } = await supabase
          .from('parts_used')
          .insert({
            repair_job_id: data.id,
            inventory_item_id: part.id,
            quantity: part.quantity,
            cost: part.cost
          });

        if (insertError) {
          console.error(`Error inserting part ${part.id}:`, insertError);
        }
      }

      // Update inventory quantities
      await updateInventoryForRepair(existingRepair?.partsUsed || [], repair.partsUsed);
    }

    return {
      id: generateShortId(data.id),
      dbId: data.id,
      customerId: data.customer_id,
      customerName: data.customers.name,
      deviceType: data.device_type,
      brand: data.brand,
      model: data.model,
      serialNumber: data.serial_number || undefined,
      issueDescription: data.issue_description,
      technicianId: data.technician_id || undefined,
      technicianName: data.technician_name || undefined,
      status: data.status as RepairStatus,
      estimatedCost: data.estimated_cost || undefined,
      finalCost: data.final_cost || undefined,
      partsUsed: repair.partsUsed, // Maintain the existing parts used
      notes: data.notes || undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      completedAt: data.completed_at ? new Date(data.completed_at) : undefined,
      deliveredAt: data.delivered_at ? new Date(data.delivered_at) : undefined
    };
  } catch (error) {
    console.error("Error in updateRepairJob:", error);
    throw error;
  }
}

export async function deleteRepairJob(id: string): Promise<boolean> {
  try {
    if (!id) throw new Error("Repair job ID is required");

    // Check if repair job has any related invoices
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('id')
      .eq('repair_job_id', id);

    if (invoicesError) {
      console.error("Error checking for related invoices:", invoicesError);
      throw new Error(`Failed to check for related invoices: ${invoicesError.message}`);
    }

    // If repair job has invoices, don't allow deletion
    if (invoices && invoices.length > 0) {
      throw new Error(`Cannot delete repair job with existing invoices. Please delete the invoices first.`);
    }

    // Delete related parts_used entries first
    const { error: partsError } = await supabase
      .from('parts_used')
      .delete()
      .eq('repair_job_id', id);

    if (partsError) {
      console.error("Error deleting parts used:", partsError);
      throw new Error(`Failed to delete related parts: ${partsError.message}`);
    }

    // Delete the repair job
    const { error } = await supabase
      .from('repair_jobs')
      .delete()
      .eq('id', id);

    if (error) {
      console.error("Error deleting repair job:", error);
      throw new Error(`Failed to delete repair job: ${error.message}`);
    }

    console.log("Repair job deleted successfully:", id);
    return true;
  } catch (error) {
    console.error("Unexpected error in deleteRepairJob:", error);
    throw error instanceof Error
      ? error
      : new Error("An unexpected error occurred while deleting repair job");
  }
}

/**
 * Update inventory quantities based on parts used in a repair
 * @param oldParts Previous parts used in the repair
 * @param newParts New parts used in the repair
 */
async function updateInventoryForRepair(
  oldParts: Array<{ id: string; quantity: number }>,
  newParts: Array<{ id: string; quantity: number }>
): Promise<void> {
  try {
    // Create a map of old parts for easy lookup
    const oldPartsMap = new Map<string, number>();
    for (const part of oldParts) {
      oldPartsMap.set(part.id, part.quantity);
    }

    // Process each new part
    for (const newPart of newParts) {
      const oldQuantity = oldPartsMap.get(newPart.id) || 0;
      const quantityDifference = newPart.quantity - oldQuantity;

      // Skip if quantity hasn't changed
      if (quantityDifference === 0) continue;

      try {
        // Get current inventory item
        const inventoryItem = await getInventoryItemById(newPart.id);
        if (!inventoryItem) {
          console.warn(`Inventory item ${newPart.id} not found`);
          continue;
        }

        // Update inventory quantity
        // If quantityDifference is positive, we're using more parts (decrease inventory)
        // If quantityDifference is negative, we're using fewer parts (increase inventory)
        const newQuantity = Math.max(0, inventoryItem.quantity - quantityDifference);

        await updateInventoryItem({
          ...inventoryItem,
          quantity: newQuantity
        });

        console.log(`Updated inventory for ${inventoryItem.name}: ${inventoryItem.quantity} -> ${newQuantity}`);
      } catch (error) {
        console.error(`Error updating inventory for part ${newPart.id}:`, error);
      }
    }

    // Check for parts that were removed entirely
    for (const [oldPartId, oldQuantity] of oldPartsMap.entries()) {
      // If this old part doesn't exist in the new parts list
      if (!newParts.some(p => p.id === oldPartId)) {
        try {
          // Get current inventory item
          const inventoryItem = await getInventoryItemById(oldPartId);
          if (!inventoryItem) {
            console.warn(`Inventory item ${oldPartId} not found`);
            continue;
          }

          // Return the parts to inventory (increase quantity)
          const newQuantity = inventoryItem.quantity + oldQuantity;

          await updateInventoryItem({
            ...inventoryItem,
            quantity: newQuantity
          });

          console.log(`Returned ${oldQuantity} of ${inventoryItem.name} to inventory: ${inventoryItem.quantity} -> ${newQuantity}`);
        } catch (error) {
          console.error(`Error updating inventory for removed part ${oldPartId}:`, error);
        }
      }
    }
  } catch (error) {
    console.error("Error updating inventory for repair:", error);
  }
}

/**
 * Get all repair jobs for a specific customer
 * @param customerId The ID of the customer
 * @returns Array of repair jobs for the customer
 */
export async function getRepairJobsByCustomerId(customerId: string): Promise<RepairJob[]> {
  try {
    // Try to fetch from database first
    const { data, error } = await supabase
      .from('repair_jobs')
      .select(`
        *,
        customers(name)
      `)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching customer repair jobs:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      // If no data in database, return empty array
      return [];
    }

    // Map database records to application model
    return data.map(item => {
      // Try to parse parts_used if it exists
      let partsUsed: Array<{ id: string; name: string; cost: number; quantity: number }> = [];
      const itemWithPartsUsed = item as any;

      if (itemWithPartsUsed.parts_used) {
        try {
          // Parse the JSONB data
          const parsedParts = typeof itemWithPartsUsed.parts_used === 'string'
            ? JSON.parse(itemWithPartsUsed.parts_used)
            : itemWithPartsUsed.parts_used;

          if (Array.isArray(parsedParts)) {
            partsUsed = parsedParts;
          }
        } catch (error) {
          console.error("Error parsing parts_used JSON:", error);
        }
      }

      return {
        id: generateShortId(item.id),
        dbId: item.id,
        customerId: item.customer_id,
        customerName: item.customers.name,
        deviceType: item.device_type,
        brand: item.brand,
        model: item.model,
        serialNumber: item.serial_number || undefined,
        issueDescription: item.issue_description,
        technicianId: item.technician_id || undefined,
        technicianName: item.technician_name || undefined,
        status: item.status as RepairStatus,
        estimatedCost: item.estimated_cost || undefined,
        finalCost: item.final_cost || undefined,
        partsUsed: partsUsed,
        notes: item.notes || undefined,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at),
        completedAt: item.completed_at ? new Date(item.completed_at) : undefined,
        deliveredAt: item.delivered_at ? new Date(item.delivered_at) : undefined
      };
    });
  } catch (error) {
    console.error('Unexpected error in getRepairJobsByCustomerId:', error);
    throw error;
  }
}
