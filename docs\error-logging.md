# Error Logging in the Mobile Repair Shop Management System

This document describes the error logging system implemented in the application, which captures and logs errors to both the console and Vercel.

## Overview

The error logging system is designed to:

1. Capture and log all types of errors (JavaScript exceptions, API errors, React rendering errors)
2. Send error data to Vercel for monitoring and analysis
3. Provide context about where and when errors occur
4. Support different severity levels for errors
5. Capture console errors and warnings

## Components of the Error Logging System

### 1. Error Logger Utility (`src/utils/errorLogger.ts`)

The core utility that handles error logging with the following features:

- **Severity Levels**: INFO, WARNING, ERROR, CRITICAL
- **Structured Error Objects**: Includes message, stack trace, timestamp, URL, and user agent
- **Context Support**: Allows adding component name, action, and additional data
- **Console Integration**: Logs to the appropriate console method based on severity
- **Vercel Analytics Integration**: Sends error data to Vercel Analytics

### 2. Global Error Handlers

Sets up listeners for:

- **Unhandled Promise Rejections**: `window.addEventListener('unhandledrejection', ...)`
- **Global Errors**: `window.addEventListener('error', ...)`
- **Page Unload with Errors**: `window.addEventListener('beforeunload', ...)`

### 3. Console Capturing

Overrides console methods to capture all console errors and warnings:

- **console.error**: Captured and logged with ERROR severity
- **console.warn**: Captured and logged with WARNING severity

### 4. Error Boundary Component (`src/components/ErrorBoundary.tsx`)

A React Error Boundary that:

- Catches errors during rendering
- Logs them with context about the component stack
- Displays a user-friendly fallback UI
- Provides a "Try Again" option to recover

### 5. Error Logger Hook (`src/hooks/useErrorLogger.ts`)

A custom hook for components to log errors with:

- **Component Context**: Automatically includes the component name
- **Severity Methods**: `logInfo`, `logWarning`, `logError`, `logCritical`
- **Async Function Wrapper**: `withErrorLogging` to wrap async functions with error logging

## Usage Examples

### Basic Error Logging

```tsx
import { logError, ErrorSeverity } from '@/utils/errorLogger';

try {
  // Some code that might throw
} catch (error) {
  logError(error, ErrorSeverity.ERROR, {
    component: 'MyComponent',
    action: 'fetchData'
  });
}
```

### Using the Error Logger Hook

```tsx
import { useErrorLogger } from '@/hooks';

function MyComponent() {
  const { logError, withErrorLogging } = useErrorLogger('MyComponent');
  
  const fetchData = withErrorLogging(async () => {
    const response = await fetch('/api/data');
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    return response.json();
  }, 'fetchData');
  
  // Use fetchData() which will automatically log errors
}
```

### Using the Error Boundary

```tsx
import ErrorBoundary from '@/components/ErrorBoundary';

function App() {
  return (
    <ErrorBoundary>
      <MyComponent />
    </ErrorBoundary>
  );
}
```

## Vercel Integration

Errors logged by the system are sent to Vercel Analytics, which provides:

1. **Error Monitoring**: View all errors in the Vercel dashboard
2. **Error Grouping**: Similar errors are grouped for easier analysis
3. **Environment Context**: See which environments (production, preview) errors occur in
4. **User Impact**: Understand how many users are affected by each error
5. **Trend Analysis**: Track error frequency over time

## Viewing Errors in Vercel

To view logged errors:

1. Go to your Vercel project dashboard
2. Navigate to the "Analytics" tab
3. Select "Errors" from the sidebar
4. Filter by time period, environment, or error type

## Best Practices

1. **Use the Error Logger Hook**: Prefer using `useErrorLogger` in components for consistent context
2. **Provide Meaningful Context**: Always include the action being performed when logging errors
3. **Use Appropriate Severity**: Reserve CRITICAL for errors that severely impact user experience
4. **Wrap Async Operations**: Use `withErrorLogging` for all async operations
5. **Add Error Boundaries**: Place Error Boundaries strategically around important components

## Extending the System

To extend the error logging system:

1. **Add Custom Context**: Extend the `ErrorContext` interface with additional fields
2. **Integrate with Other Services**: Modify the `logError` function to send to additional services
3. **Add User Context**: Include user ID or session information when available
4. **Implement Rate Limiting**: Add logic to prevent flooding with repeated errors
