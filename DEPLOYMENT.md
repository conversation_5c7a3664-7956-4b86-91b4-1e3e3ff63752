# Deploying the Mobile Repair Shop Management System to Vercel

This guide provides step-by-step instructions for deploying the Mobile Repair Shop Management System to Vercel.

## Prerequisites

- A [Vercel](https://vercel.com) account
- Git repository with your code (GitHub, GitLab, or Bitbucket)
- Node.js installed on your local machine

## Deployment Steps

### 1. Prepare Your Repository

Ensure your code is committed to a Git repository. The repository should include:

- All application code
- `vercel.json` configuration file
- `package.json` with proper build scripts

### 2. Connect to Vercel

1. Log in to your Vercel account
2. Click "Add New" > "Project"
3. Import your Git repository
4. Configure the project:
   - **Framework Preset**: Vite
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`

### 3. Configure Environment Variables (Optional)

The current application has hardcoded Supabase credentials, but for better security, you can use environment variables:

1. In the Vercel project settings, go to "Environment Variables"
2. Add the following variables:
   - `VITE_SUPABASE_URL`: Your Supabase URL
   - `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key

### 4. Deploy

1. Click "Deploy"
2. Wait for the build and deployment to complete
3. Once deployed, Vercel will provide a URL to access your application

## Vercel CLI Deployment (Alternative)

You can also deploy using the Vercel CLI:

1. Install the Vercel CLI:
   ```
   npm install -g vercel
   ```

2. Log in to Vercel:
   ```
   vercel login
   ```

3. Navigate to your project directory and deploy:
   ```
   cd /path/to/your/project
   vercel
   ```

4. Follow the prompts to configure your deployment

## Troubleshooting

### MIME Type Errors

If you encounter MIME type errors with Vite dependencies:

1. Ensure `base: './'` is set in your `vite.config.ts` file
2. Check that the `vercel.json` file has the correct route configuration

### Routing Issues

If you have issues with client-side routing:

1. Ensure the catch-all route in `vercel.json` is correctly configured
2. Verify that your React Router is using the correct base path

### Supabase Connection Issues

If your application cannot connect to Supabase:

1. Check that the Supabase URL and key are correct
2. Verify that your Supabase project has the correct CORS settings
3. Ensure your Supabase RLS (Row Level Security) policies are properly configured

## Production Considerations

### Security

- Consider implementing proper authentication in Version 2
- Use environment variables for sensitive information
- Configure proper CORS settings in Supabase

### Performance

- Enable Vercel's Edge Network for better global performance
- Consider implementing caching strategies for frequently accessed data
- Use Vercel's Analytics to monitor performance

### Monitoring

- Set up Vercel Analytics to track usage and performance
- Configure alerts for any deployment or runtime issues
- Regularly check Supabase logs for database-related issues

## Updating Your Deployment

To update your deployment:

1. Make changes to your code
2. Commit and push to your Git repository
3. Vercel will automatically detect changes and redeploy

Alternatively, you can manually trigger a deployment from the Vercel dashboard or using the CLI:

```
vercel --prod
```

## Custom Domains

To use a custom domain:

1. Go to your project settings in Vercel
2. Navigate to "Domains"
3. Add your custom domain
4. Follow the instructions to configure DNS settings

## Conclusion

Your Mobile Repair Shop Management System should now be successfully deployed to Vercel and accessible via the provided URL. For any issues or further customization, refer to the [Vercel documentation](https://vercel.com/docs) or the [Vite deployment guide](https://vitejs.dev/guide/static-deploy.html).
