
import React, { useState, useEffect, useRef } from "react";
import { Check, ChevronsUpDown, PlusCircle, User, Search, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Customer } from "@/lib/data";
import { getCustomers, searchCustomers } from "@/services/customers";
import { toast } from "@/components/ui/sonner";
import {
  CustomerSearchDialog,
  CustomerSearchDialogContent,
} from "@/components/ui/customer-search-dialog";

interface CustomerSearchProps {
  onSelectCustomer: (customer: Customer) => void;
  onCreateNew: () => void;
}

const CustomerSearch: React.FC<CustomerSearchProps> = ({
  onSelectCustomer,
  onCreateNew,
}) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const fetchCustomers = async () => {
      setIsLoading(true);
      try {
        const data = await getCustomers();
        setCustomers(data);
        setFilteredCustomers(data);
      } catch (error) {
        console.error("Error fetching customers:", error);
        toast.error("Failed to load customers");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomers();
  }, []);

  // Filter customers when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredCustomers(customers);
      return;
    }

    const search = async () => {
      setIsLoading(true);
      try {
        // Use the searchCustomers function from the service
        const results = await searchCustomers(searchTerm);
        setFilteredCustomers(results);
      } catch (error) {
        console.error("Error searching customers:", error);
        // Fall back to client-side filtering if API search fails
        const filtered = customers.filter(customer =>
          customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          customer.phone.includes(searchTerm) ||
          (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase()))
        );
        setFilteredCustomers(filtered);
      } finally {
        setIsLoading(false);
      }
    };

    // Debounce search to avoid too many API calls
    const timer = setTimeout(() => {
      search();
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, customers]);

  const handleSelectCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    onSelectCustomer(customer);
    setIsDropdownOpen(false);
    setSearchTerm("");
  };

  const handleClearSelection = () => {
    setSelectedCustomer(null);
    setSearchTerm("");
    setFilteredCustomers(customers);
  };

  const openDropdown = () => {
    setIsDropdownOpen(true);
    // Focus the search input when dropdown opens
    setTimeout(() => searchInputRef.current?.focus(), 100);
  };

  const closeDropdown = () => {
    setIsDropdownOpen(false);
  };

  return (
    <div className="space-y-2">
      <div className="flex justify-between">
        <div className="relative w-full">
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isDropdownOpen}
            className="w-full justify-between"
            disabled={isLoading}
            onClick={openDropdown}
            type="button"
          >
            {selectedCustomer ? (
              <div className="flex items-center">
                <User className="mr-2 h-4 w-4" />
                <span>{selectedCustomer.name}</span>
              </div>
            ) : (
              <span className="text-muted-foreground">Search for a customer...</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>

          <CustomerSearchDialog open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <CustomerSearchDialogContent
              className="p-0 max-w-[350px]"
              onInteractOutside={closeDropdown}
              onEscapeKeyDown={closeDropdown}
            >
              <div className="flex items-center border-b p-2">
                <Search className="mr-2 h-4 w-4 shrink-0 opacity-70" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search by name, phone, or email"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSearchTerm("")}
                    className="h-6 w-6"
                    type="button"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <ScrollArea className="h-[300px]">
                {isLoading ? (
                  <div className="p-4 text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary mx-auto"></div>
                    <p className="text-sm text-muted-foreground mt-2">Searching...</p>
                  </div>
                ) : filteredCustomers.length === 0 ? (
                  <div className="p-4 text-center">
                    <p className="text-sm text-muted-foreground">No customers found</p>
                    <Button
                      variant="link"
                      onClick={onCreateNew}
                      className="mt-2 text-sm"
                      type="button"
                    >
                      Create new customer
                    </Button>
                  </div>
                ) : (
                  <div>
                    {filteredCustomers.map((customer) => (
                      <div
                        key={customer.id}
                        className={cn(
                          "flex flex-col px-4 py-2 cursor-pointer hover:bg-muted",
                          selectedCustomer?.id === customer.id && "bg-muted"
                        )}
                        onClick={() => handleSelectCustomer(customer)}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{customer.name}</span>
                          {selectedCustomer?.id === customer.id && (
                            <Check className="h-4 w-4" />
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {customer.phone}
                          {customer.email && ` | ${customer.email}`}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
              <div className="p-2 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={onCreateNew}
                  type="button"
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add New Customer
                </Button>
              </div>
            </CustomerSearchDialogContent>
          </CustomerSearchDialog>
        </div>

        {selectedCustomer && (
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClearSelection}
            className="ml-2"
            title="Clear selection"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {selectedCustomer && (
        <div className="mt-2 p-3 bg-blue-50 rounded-md border border-blue-100">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <div>
              <p className="font-medium text-gray-500">Name</p>
              <p>{selectedCustomer.name}</p>
            </div>
            <div>
              <p className="font-medium text-gray-500">Phone</p>
              <p>{selectedCustomer.phone}</p>
            </div>
            <div>
              <p className="font-medium text-gray-500">Email</p>
              <p>{selectedCustomer.email}</p>
            </div>
            {selectedCustomer.address && (
              <div>
                <p className="font-medium text-gray-500">Address</p>
                <p>{selectedCustomer.address}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerSearch;
