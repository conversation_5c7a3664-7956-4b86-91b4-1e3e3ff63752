
import React, { useState, useEffect } from "react";
import { AppProvider } from "@/contexts/AppContext";
import DashboardStats from "@/components/Dashboard/DashboardStats";
import RepairStatusChart from "@/components/Dashboard/RepairStatusChart";
import RecentRepairs from "@/components/Dashboard/RecentRepairs";
import Header from "@/components/Layout/Header";
import Sidebar from "@/components/Layout/Sidebar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import RepairForm from "@/components/Repair/RepairForm";
import RepairTicketPrint from "@/components/Repair/RepairTicketPrint";
import { getDashboardData } from "@/services/dashboard";
import { RepairJob } from "@/lib/data";
import { toast } from "@/components/ui/sonner";

const Index = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [activeTab, setActiveTab] = useState("dashboard");
  const [dashboardData, setDashboardData] = useState({
    statusCounts: {
      pending: 0,
      inProgress: 0,
      completed: 0,
      delivered: 0,
      cancelled: 0,
      on_hold: 0
    },
    revenueSummary: {
      today: 0,
      thisWeek: 0,
      thisMonth: 0,
      pending: 0
    },
    inventoryAlerts: [],
    recentRepairs: []
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        const data = await getDashboardData();
        setDashboardData(data);
      } catch (error) {
        console.error("Error loading dashboard data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  const [showingPrintDialog, setShowingPrintDialog] = useState(false);
  const [newRepairData, setNewRepairData] = useState<RepairJob | null>(null);

  const handleRepairCreated = (repair?: RepairJob) => {
    if (repair) {
      // If we have repair data, show success toast and print dialog
      toast.success("Repair job created successfully", {
        description: `Repair #${repair.id} for ${repair.customerName} has been created.`,
      });
      setNewRepairData(repair);
      setShowingPrintDialog(true);
    } else {
      // Otherwise just return to dashboard
      toast.success("Repair job created successfully");
      setActiveTab("dashboard");
    }
  };

  const handlePrintDialogClosed = () => {
    setShowingPrintDialog(false);
    setActiveTab("dashboard");
  };

  return (
    <AppProvider>
      <div className="flex min-h-screen">
        <Sidebar isOpen={sidebarOpen} toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? "md:ml-64" : "md:ml-16"}`}>
          <Header toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isOpen={sidebarOpen} />

          <main className="px-4 py-6 md:px-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Mobile Repair Shop</h1>
                <TabsList>
                  <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                  <TabsTrigger value="new-repair">New Repair</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="dashboard" className="space-y-6">
                <DashboardStats
                  statusCounts={dashboardData.statusCounts}
                  revenueSummary={dashboardData.revenueSummary}
                  lowStockItems={dashboardData.inventoryAlerts}
                  isLoading={isLoading}
                />

                <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
                  <RepairStatusChart
                    statusCounts={dashboardData.statusCounts}
                    isLoading={isLoading}
                  />
                  <RecentRepairs
                    recentRepairs={dashboardData.recentRepairs}
                    isLoading={isLoading}
                  />
                </div>
              </TabsContent>

              <TabsContent value="new-repair">
                <Card>
                  <CardHeader>
                    <CardTitle>Create New Repair Job</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <RepairForm onSuccess={handleRepairCreated} />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </main>
        </div>
      </div>

      {/* Print Ticket Dialog */}
      {showingPrintDialog && newRepairData && (
        <Dialog open={showingPrintDialog} onOpenChange={setShowingPrintDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Print Repair Ticket</DialogTitle>
              <DialogDescription>
                Would you like to print a repair ticket for this repair job?
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <p className="text-sm text-muted-foreground mb-4">
                Printing a repair ticket allows you to attach it to the device for easy identification throughout the repair process.
              </p>

              <div className="flex justify-center">
                <RepairTicketPrint
                  repair={newRepairData}
                  buttonText="Print Repair Ticket"
                  buttonVariant="default"
                  buttonSize="default"
                  className="w-full"
                  onPrintSuccess={() => {
                    toast.success("Repair ticket printed successfully");
                    handlePrintDialogClosed();
                  }}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => {
                toast.info("Printing skipped");
                handlePrintDialogClosed();
              }}>
                Skip Printing
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </AppProvider>
  );
};

export default Index;
