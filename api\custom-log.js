// Simple custom logging API that logs to Vercel server logs
// This will work even on the free plan

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }
  
  try {
    const { type, message, details } = req.body;
    
    // Generate a timestamp
    const timestamp = new Date().toISOString();
    
    // Log to Vercel server logs (visible in Vercel dashboard)
    console.log(`[CUSTOM_LOG] [${timestamp}] [${type}] ${message}`);
    
    // Log details if provided
    if (details) {
      console.log(`[CUSTOM_LOG_DETAILS] ${JSON.stringify(details, null, 2)}`);
    }
    
    // Return success
    return res.status(200).json({ 
      success: true, 
      timestamp,
      message: 'Log recorded successfully'
    });
  } catch (error) {
    console.error('Error in custom logging:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error recording log',
      error: error.message
    });
  }
}
