import { test, expect } from '@playwright/test';

test.describe('Invoice Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:5173/');
  });

  test('should display invoices list', async ({ page }) => {
    // Navigate to invoices page
    await page.click('text=Invoices');
    
    // Check if we're on the invoices page
    await expect(page.locator('h1')).toContainText('Invoices');
    
    // Check if the invoices table is displayed
    await expect(page.locator('table')).toBeVisible();
    
    // Check if there are invoice rows
    const invoiceRows = page.locator('table tbody tr');
    const count = await invoiceRows.count();
    expect(count).toBeGreaterThan(0);
  });

  test('should view invoice details', async ({ page }) => {
    // Navigate to invoices page
    await page.click('text=Invoices');
    
    // Click on the view button of the first invoice
    await page.locator('table tbody tr').first().locator('button').click();
    
    // Check if the invoice detail dialog is displayed
    await expect(page.locator('div[role="dialog"]')).toBeVisible();
    await expect(page.locator('div[role="dialog"] h2')).toContainText('Invoice #');
    
    // Check if customer information is displayed
    await expect(page.locator('text=Customer Information')).toBeVisible();
    
    // Check if payment information is displayed
    await expect(page.locator('text=Payment Information')).toBeVisible();
    
    // Check if invoice items are displayed
    await expect(page.locator('text=Invoice Items')).toBeVisible();
    
    // Close the dialog
    await page.keyboard.press('Escape');
  });

  test('should generate invoice from completed repair', async ({ page }) => {
    // Navigate to repairs page
    await page.click('text=Repairs');
    
    // Find a completed repair
    const completedRepairRow = page.locator('table tbody tr').filter({ 
      has: page.locator('span:has-text("Completed")') 
    }).first();
    
    // If there's a completed repair, test invoice generation
    if (await completedRepairRow.count() > 0) {
      // Click the generate invoice button (second button in the actions column)
      await completedRepairRow.locator('button').nth(1).click();
      
      // Check if the invoice form dialog is displayed
      await expect(page.locator('div[role="dialog"]')).toBeVisible();
      await expect(page.locator('div[role="dialog"] h2')).toContainText('Generate Invoice');
      
      // Fill in the invoice form
      await page.fill('input#laborCost', '1500');
      await page.fill('input#taxRate', '18');
      await page.fill('input#paidAmount', '10000');
      
      // Submit the form
      await page.click('button:has-text("Create Invoice")');
      
      // Check if the invoice preview dialog is displayed
      await expect(page.locator('div[role="dialog"] h2')).toContainText('Invoice Preview');
      
      // Check if the invoice details are displayed
      await expect(page.locator('text=Customer Information')).toBeVisible();
      await expect(page.locator('text=Payment Information')).toBeVisible();
      await expect(page.locator('text=Invoice Items')).toBeVisible();
      
      // Close the dialog
      await page.keyboard.press('Escape');
    } else {
      console.log('No completed repairs found to test invoice generation');
    }
  });

  test('should download PDF invoice', async ({ page }) => {
    // Navigate to invoices page
    await page.click('text=Invoices');
    
    // Click on the view button of the first invoice
    await page.locator('table tbody tr').first().locator('button').click();
    
    // Check if the invoice detail dialog is displayed
    await expect(page.locator('div[role="dialog"]')).toBeVisible();
    
    // Start waiting for download before clicking
    const downloadPromise = page.waitForEvent('download');
    
    // Click the download PDF button
    await page.locator('button:has-text("Download PDF")').click();
    
    // Wait for the download to start
    const download = await downloadPromise;
    
    // Verify that the download started and has the correct name pattern
    expect(download.suggestedFilename()).toMatch(/Invoice-.*\.pdf/);
    
    // Close the dialog
    await page.keyboard.press('Escape');
  });
});
