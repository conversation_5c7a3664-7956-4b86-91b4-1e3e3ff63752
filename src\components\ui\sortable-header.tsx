import React from 'react';
import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { SortConfig, SortDirection } from '@/hooks/useSorting';

interface SortableHeaderProps<T> {
  column: {
    key: keyof T;
    label: string;
    className?: string;
  };
  sortConfig: SortConfig<T> | null;
  onSort: (key: keyof T) => void;
}

export function SortableHeader<T>({
  column,
  sortConfig,
  onSort,
}: SortableHeaderProps<T>) {
  const isSorted = sortConfig?.key === column.key;
  const direction = isSorted ? sortConfig.direction : null;
  
  return (
    <th 
      className={`py-3 px-4 cursor-pointer select-none ${column.className || ''}`}
      onClick={() => onSort(column.key)}
    >
      <div className="flex items-center gap-1">
        <span>{column.label}</span>
        <span className="inline-flex">
          {!isSorted && <ArrowUpDown className="h-4 w-4 text-muted-foreground/70" />}
          {direction === 'asc' && <ArrowUp className="h-4 w-4 text-primary" />}
          {direction === 'desc' && <ArrowDown className="h-4 w-4 text-primary" />}
        </span>
      </div>
    </th>
  );
}
