import React, { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Customer, RepairJob } from "@/lib/data";
import { toast } from "@/components/ui/sonner";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import CustomerForm from "@/components/Customer/CustomerForm";
import CustomerSearch from "@/components/Customer/CustomerSearch";
import { createRepair<PERSON><PERSON>, update<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/services/repairs";
import { getCustomerById } from "@/services/customers";

const repairSchema = z.object({
  deviceType: z.string().min(1, { message: "Device type is required" }),
  brand: z.string().min(1, { message: "Brand is required" }),
  customBrand: z.string()
    .max(50, { message: "Brand name must be 50 characters or less" })
    .optional(),
  model: z.string().min(1, { message: "Model is required" }),
  serialNumber: z.string().optional(),
  issueDescription: z
    .string()
    .min(10, { message: "Please provide a detailed description of the issue" }),
  estimatedCost: z
    .string()
    .refine((val) => val === "" || !isNaN(parseFloat(val)), {
      message: "Must be a valid number or empty",
    })
    .optional()
    .transform((val) => val === "" ? undefined : val),
}).refine((data) => {
  // If "Other" is selected for brand, customBrand must be provided and valid
  if (data.brand === "Other") {
    if (!data.customBrand || data.customBrand.trim().length < 2) {
      return false;
    }
    // Check for valid characters (letters, numbers, spaces, hyphens, and common punctuation)
    const validBrandRegex = /^[a-zA-Z0-9\s\-&.()]+$/;
    return validBrandRegex.test(data.customBrand.trim());
  }
  return true;
}, {
  message: "Custom brand name is required, must be at least 2 characters, and contain only letters, numbers, spaces, and common punctuation",
  path: ["customBrand"],
});

type RepairFormValues = z.infer<typeof repairSchema>;

interface RepairFormProps {
  onSuccess: (repair?: RepairJob) => void;
  repair?: RepairJob; // Optional repair for editing mode
}

const RepairForm: React.FC<RepairFormProps> = ({ onSuccess, repair }) => {
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isNewCustomerDialogOpen, setIsNewCustomerDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isOtherBrandSelected, setIsOtherBrandSelected] = useState(false);
  // No longer need the print dialog state
  const isEditMode = !!repair;

  const form = useForm<RepairFormValues>({
    resolver: zodResolver(repairSchema),
    defaultValues: {
      deviceType: repair?.deviceType || "",
      brand: repair?.brand || "",
      customBrand: "",
      model: repair?.model || "",
      serialNumber: repair?.serialNumber || "",
      issueDescription: repair?.issueDescription || "",
      estimatedCost: repair?.estimatedCost ? String(repair.estimatedCost) : "",
    },
  });

  // Load customer data when in edit mode and handle custom brand
  useEffect(() => {
    if (repair && repair.customerId) {
      const loadCustomer = async () => {
        try {
          const customer = await getCustomerById(repair.customerId);
          if (customer) {
            setSelectedCustomer(customer);
          }
        } catch (error) {
          console.error("Error loading customer for repair:", error);
          toast.error("Failed to load customer information");
        }
      };

      loadCustomer();
    }

    // Handle custom brand in edit mode
    if (repair && repair.brand) {
      const isCustomBrand = !commonBrands.includes(repair.brand);
      if (isCustomBrand) {
        setIsOtherBrandSelected(true);
        form.setValue("brand", "Other");
        form.setValue("customBrand", repair.brand);
      }
    }
  }, [repair, form]);

  const onSubmit = async (data: RepairFormValues) => {
    if (!selectedCustomer) {
      toast.error("Please select a customer");
      return;
    }

    setIsSubmitting(true);
    try {
      // Determine the final brand value
      const finalBrand = data.brand === "Other" ? data.customBrand || "Other" : data.brand;

      if (isEditMode && repair) {
        // Update existing repair
        const updatedRepair = await updateRepairJob({
          ...repair,
          customerId: selectedCustomer.id,
          customerName: selectedCustomer.name,
          deviceType: data.deviceType,
          brand: finalBrand,
          model: data.model,
          serialNumber: data.serialNumber,
          issueDescription: data.issueDescription,
          estimatedCost: data.estimatedCost ? parseFloat(data.estimatedCost) : null
        });

        onSuccess();
      } else {
        // Create new repair
        const createdRepair = await createRepairJob({
          customerId: selectedCustomer.id,
          customerName: selectedCustomer.name,
          deviceType: data.deviceType,
          brand: finalBrand,
          model: data.model,
          serialNumber: data.serialNumber,
          issueDescription: data.issueDescription,
          status: "pending",
          estimatedCost: data.estimatedCost ? parseFloat(data.estimatedCost) : null
        });

        // Pass the created repair to the parent component
        onSuccess(createdRepair);

        // Reset form
        form.reset();
        setSelectedCustomer(null);
        setIsOtherBrandSelected(false);
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} repair job:`, error);
      toast.error(`Failed to ${isEditMode ? 'update' : 'create'} repair job`, {
        description: "Please try again or contact support."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const deviceTypes = ["Smartphone", "Tablet", "Laptop", "Smartwatch", "Keypad Phone", "Other"];
  const commonBrands = ["Apple", "Samsung", "Google", "Xiaomi", "OnePlus", "Oppo", "Vivo", "Realme", "Other"];

  const handleCustomerCreated = async (customer: Customer) => {
    try {
      if (customer) {
        setSelectedCustomer(customer);
        setIsNewCustomerDialogOpen(false);
        toast.success("Customer selected", {
          description: `${customer.name} has been selected for this repair job.`
        });
      }
    } catch (error) {
      console.error("Error handling created customer:", error);
      toast.error("Failed to process customer details");
    }
  };

  return (
    <>
      <div className="mb-6">
        <h2 className="text-lg font-medium mb-2">Customer Information</h2>
        <div className="z-10 relative">
          <CustomerSearch
            onSelectCustomer={(customer) => {
              setSelectedCustomer(customer);
              toast.success("Customer selected", {
                description: `${customer.name} has been selected for this repair job.`
              });
            }}
            onCreateNew={() => setIsNewCustomerDialogOpen(true)}
          />
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <h2 className="text-lg font-medium">Device Information</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <FormField
              control={form.control}
              name="deviceType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Device Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select device type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {deviceTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="brand"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Brand</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setIsOtherBrandSelected(value === "Other");
                      if (value !== "Other") {
                        form.setValue("customBrand", "");
                      }
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select brand" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {commonBrands.map((brand) => (
                        <SelectItem key={brand} value={brand}>
                          {brand}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            {isOtherBrandSelected && (
              <FormField
                control={form.control}
                name="customBrand"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Brand Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter brand name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <FormField
              control={form.control}
              name="model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Model</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., iPhone 12 Pro" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="serialNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Serial Number / IMEI (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="Enter device serial number" {...field} />
                </FormControl>
                <FormDescription>
                  This helps identify the device uniquely
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="issueDescription"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Issue Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Please describe the issue in detail..."
                    {...field}
                    rows={4}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="estimatedCost"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Estimated Cost (Optional)</FormLabel>
                <FormControl>
                  <Input type="number" step="0.01" min="0" placeholder="0.00" {...field} />
                </FormControl>
                <FormDescription>
                  Initial estimate for the repair
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              type="button"
              onClick={() => form.reset()}
              disabled={isSubmitting}
            >
              Reset
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? (isEditMode ? "Updating..." : "Creating...")
                : (isEditMode ? "Update Repair Job" : "Create Repair Job")
              }
            </Button>
          </div>
        </form>
      </Form>

      {/* New Customer Dialog */}
      <Dialog open={isNewCustomerDialogOpen} onOpenChange={setIsNewCustomerDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Customer</DialogTitle>
            <DialogDescription>Create a new customer record</DialogDescription>
          </DialogHeader>
          <CustomerForm
            onSuccess={handleCustomerCreated}
          />
        </DialogContent>
      </Dialog>

      {/* Print dialog moved to Index component */}
    </>
  );
};

export default RepairForm;
