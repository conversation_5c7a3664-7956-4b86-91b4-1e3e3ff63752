
import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import {
  Customer,
  RepairJob,
  InventoryItem,
  Invoice,
  Payment
} from "@/lib/data";
import { getCustomers, createCustomer as createCustomerService, updateCustomer as updateCustomerService, deleteCustomer as deleteCustomerService } from "@/services/customers";
import { getInvoices, createInvoice as createInvoiceService, updateInvoice as updateInvoiceService, addPayment as addPaymentService, deleteInvoice as deleteInvoiceService } from "@/services/invoices";
import { getPaymentsByInvoiceId } from "@/services/payments";
import { getRepairJobs as getRepairJobsService, createRepairJob as createRepairJobService, updateRepairJob as updateRepairJobService, deleteRepairJob as deleteRepairJobService } from "@/services/repairs";
import { getInventoryItems, createInventoryItem as createInventoryItemService, updateInventoryItem as updateInventoryItemService, deleteInventoryItem as deleteInventoryItemService } from "@/services/inventory";
import { toast } from "@/components/ui/sonner";

type AppContextType = {
  customers: Customer[];
  repairJobs: RepairJob[];
  inventory: InventoryItem[];
  invoices: Invoice[];
  addCustomer: (customer: Omit<Customer, "id" | "createdAt">) => Promise<Customer>;
  updateCustomer: (customer: Customer) => Promise<Customer>;
  deleteCustomer: (id: string) => Promise<boolean>;
  addRepairJob: (repair: Omit<RepairJob, "id" | "createdAt" | "updatedAt">) => RepairJob;
  updateRepairJob: (repair: RepairJob) => Promise<RepairJob>;
  deleteRepairJob: (id: string) => Promise<boolean>;
  addInventoryItem: (item: Omit<InventoryItem, "id">) => Promise<InventoryItem>;
  updateInventoryItem: (item: InventoryItem) => Promise<InventoryItem>;
  deleteInventoryItem: (id: string) => Promise<boolean>;
  addInvoice: (invoice: Omit<Invoice, "id" | "createdAt" | "invoiceNumber">) => Promise<Invoice>;
  updateInvoice: (invoice: Invoice) => Promise<Invoice>;
  deleteInvoice: (id: string) => Promise<boolean>;
  addPayment: (invoiceId: string, payment: Omit<Payment, "id" | "invoiceId">) => Promise<Payment>;
  getInvoicePayments: (invoiceId: string) => Promise<Payment[]>;
  refreshInvoices: () => Promise<void>;
  refreshRepairJobs: () => Promise<void>;
  refreshInventory: () => Promise<void>;
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Expose the context functions globally for development
  React.useEffect(() => {
    (window as any).__APP_CONTEXT__ = {
      updateRepairJob: (repair: RepairJob) => {
        const updated = {
          ...repair,
          updatedAt: new Date()
        };
        setRepairJobs(prev => prev.map(r => r.id === repair.id ? updated : r));
        return Promise.resolve(updated);
      }
    };
    return () => {
      delete (window as any).__APP_CONTEXT__;
    };
  }, []);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch customers from the database
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setIsLoading(true);
        const dbCustomers = await getCustomers();
        setCustomers(dbCustomers || []);
      } catch (error) {
        console.error("Error fetching customers:", error);
        toast.error("Failed to load customers");
        setCustomers([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomers();
  }, []);
  const [repairJobs, setRepairJobs] = useState<RepairJob[]>([]);
  const [inventory, setInventory] = useState<InventoryItem[]>([]);

  // Fetch repair jobs from the database
  const refreshRepairJobs = useCallback(async () => {
    try {
      const dbRepairJobs = await getRepairJobsService();
      if (dbRepairJobs && dbRepairJobs.length > 0) {
        setRepairJobs(dbRepairJobs);
      }
    } catch (error) {
      console.error("Error fetching repair jobs:", error);
      toast.error("Failed to load repair jobs");
    }
  }, []);

  // Initial fetch of repair jobs
  useEffect(() => {
    refreshRepairJobs();
  }, []);
  const [invoices, setInvoices] = useState<Invoice[]>([]);

  // Fetch invoices from the database
  const refreshInvoices = useCallback(async () => {
    try {
      const dbInvoices = await getInvoices();
      if (dbInvoices && dbInvoices.length > 0) {
        setInvoices(dbInvoices);
      }
    } catch (error) {
      console.error("Error fetching invoices:", error);
      toast.error("Failed to load invoices");
    }
  }, []);

  // Initial fetch of invoices
  useEffect(() => {
    refreshInvoices();
  }, []);

  const addCustomer = async (customerData: Omit<Customer, "id" | "createdAt">) => {
    try {
      // Create customer in the database
      const newCustomer = await createCustomerService(customerData);

      // Update local state
      setCustomers([...customers, newCustomer]);
      return newCustomer;
    } catch (error) {
      console.error("Error adding customer:", error);

      // More specific error message
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      if (errorMessage.includes("phone") && errorMessage.includes("already exists")) {
        toast.error("Phone number already in use", {
          description: "Please use a different phone number."
        });
      } else if (errorMessage.includes("email already exists")) {
        toast.error("Email already in use", {
          description: "Please use a different email address."
        });
      } else {
        toast.error("Failed to add customer", {
          description: errorMessage
        });
      }

      throw error;
    }
  };

  const updateCustomer = async (updatedCustomer: Customer) => {
    try {
      // Update customer in the database
      const result = await updateCustomerService(updatedCustomer);

      // Update local state
      setCustomers(customers.map(c =>
        c.id === updatedCustomer.id ? result : c
      ));
      return result;
    } catch (error) {
      console.error("Error updating customer:", error);

      // More specific error message
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      if (errorMessage.includes("phone") && errorMessage.includes("already exists")) {
        toast.error("Phone number already in use", {
          description: "Please use a different phone number."
        });
      } else if (errorMessage.includes("email already exists")) {
        toast.error("Email already in use", {
          description: "Please use a different email address."
        });
      } else {
        toast.error("Failed to update customer", {
          description: errorMessage
        });
      }

      throw error;
    }
  };

  const deleteCustomer = async (id: string) => {
    try {
      // Delete customer in the database
      await deleteCustomerService(id);

      // Update local state
      setCustomers(customers.filter(c => c.id !== id));

      toast.success("Customer deleted successfully");
      return true;
    } catch (error) {
      console.error("Error deleting customer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete customer");
      throw error;
    }
  };

  const addRepairJob = async (repairData: Omit<RepairJob, "id" | "createdAt" | "updatedAt">) => {
    try {
      // Create repair job in the database
      const newRepair = await createRepairJobService(repairData);

      // Update local state
      setRepairJobs([...repairJobs, newRepair]);
      return newRepair;
    } catch (error) {
      console.error("Error adding repair job:", error);
      toast.error("Failed to create repair job");
      throw error;
    }
  };

  const updateRepairJob = async (updatedRepair: RepairJob) => {
    try {
      // Update repair job in the database
      const result = await updateRepairJobService(updatedRepair);

      // Update local state
      setRepairJobs(repairJobs.map(r =>
        r.id === updatedRepair.id ? result : r
      ));
      return result;
    } catch (error) {
      console.error("Error updating repair job:", error);
      toast.error("Failed to update repair job");
      throw error;
    }
  };

  const deleteRepairJob = async (id: string) => {
    try {
      // Delete repair job in the database
      await deleteRepairJobService(id);

      // Update local state - filter by both id and dbId to handle both display and database IDs
      setRepairJobs(repairJobs.filter(r => r.id !== id && r.dbId !== id));

      toast.success("Repair job deleted successfully");
      return true;
    } catch (error) {
      console.error("Error deleting repair job:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete repair job");
      throw error;
    }
  };

  const addInventoryItem = async (itemData: Omit<InventoryItem, "id">) => {
    try {
      // Create inventory item in the database
      const newItem = await createInventoryItemService(itemData);

      // Update local state
      setInventory([...inventory, newItem]);
      return newItem;
    } catch (error) {
      console.error("Error adding inventory item:", error);
      toast.error("Failed to add inventory item");
      throw error;
    }
  };

  const updateInventoryItem = async (updatedItem: InventoryItem) => {
    try {
      // Update inventory item in the database
      const result = await updateInventoryItemService(updatedItem);

      // Update local state
      setInventory(inventory.map(i =>
        i.id === updatedItem.id ? result : i
      ));
      return result;
    } catch (error) {
      console.error("Error updating inventory item:", error);
      toast.error("Failed to update inventory item");
      throw error;
    }
  };

  const deleteInventoryItem = async (id: string) => {
    try {
      // Delete inventory item in the database
      await deleteInventoryItemService(id);

      // Update local state
      setInventory(inventory.filter(i => i.id !== id));

      toast.success("Inventory item deleted successfully");
      return true;
    } catch (error) {
      console.error("Error deleting inventory item:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete inventory item");
      throw error;
    }
  };

  // Fetch inventory items from the database
  const refreshInventory = useCallback(async () => {
    try {
      const dbInventoryItems = await getInventoryItems();
      setInventory(dbInventoryItems || []);
    } catch (error) {
      console.error("Error fetching inventory items:", error);
      toast.error("Failed to load inventory items");
      setInventory([]);
    }
  }, []);

  // Initial fetch of inventory items
  useEffect(() => {
    refreshInventory();
  }, [refreshInventory]);

  const addInvoice = async (invoiceData: Omit<Invoice, "id" | "createdAt" | "invoiceNumber">) => {
    try {
      // Create invoice in the database
      const newInvoice = await createInvoiceService(invoiceData);

      // Update local state
      setInvoices([...invoices, newInvoice]);
      return newInvoice;
    } catch (error) {
      console.error("Error adding invoice:", error);
      toast.error("Failed to create invoice");
      throw error;
    }
  };

  const updateInvoice = async (updatedInvoice: Invoice) => {
    try {
      // Update invoice in the database
      const result = await updateInvoiceService(updatedInvoice);

      // Update local state
      setInvoices(invoices.map(i =>
        i.id === updatedInvoice.id ? result : i
      ));
      return result;
    } catch (error) {
      console.error("Error updating invoice:", error);
      toast.error("Failed to update invoice");
      throw error;
    }
  };

  const addPayment = async (invoiceId: string, paymentData: Omit<Payment, "id" | "invoiceId">) => {
    try {
      // Ensure payment has a receipt number
      const paymentWithReceipt = {
        ...paymentData,
        // Generate a receipt number if not provided
        receiptNumber: paymentData.receiptNumber ||
          `REC-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`
      };

      // Add payment in the database
      const newPayment = await addPaymentService(invoiceId, paymentWithReceipt);

      // Refresh invoices to get updated payment status
      await refreshInvoices();

      return newPayment;
    } catch (error) {
      console.error("Error adding payment:", error);
      toast.error("Failed to add payment");
      throw error;
    }
  };

  const getInvoicePayments = async (invoiceId: string) => {
    try {
      return await getPaymentsByInvoiceId(invoiceId);
    } catch (error) {
      console.error("Error fetching payments:", error);
      toast.error("Failed to load payments");
      return [];
    }
  };

  const deleteInvoice = async (id: string) => {
    try {
      // Delete invoice in the database
      await deleteInvoiceService(id);

      // Update local state
      setInvoices(invoices.filter(invoice => invoice.id !== id));

      // Refresh repair jobs as the status might have changed
      await refreshRepairJobs();

      return true;
    } catch (error) {
      console.error("Error deleting invoice:", error);
      toast.error("Failed to delete invoice");
      throw error;
    }
  };

  return (
    <AppContext.Provider value={{
      customers,
      repairJobs,
      inventory,
      invoices,
      addCustomer,
      updateCustomer,
      deleteCustomer,
      addRepairJob,
      updateRepairJob,
      deleteRepairJob,
      addInventoryItem,
      updateInventoryItem,
      deleteInventoryItem,
      addInvoice,
      updateInvoice,
      deleteInvoice,
      addPayment,
      getInvoicePayments,
      refreshInvoices,
      refreshRepairJobs,
      refreshInventory
    }}>
      {isLoading ? (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        children
      )}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};
