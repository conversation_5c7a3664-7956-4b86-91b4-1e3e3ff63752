import { supabase } from "@/integrations/supabase/client";
import { formatINR } from "@/lib/currency";

/**
 * Get repair status counts from the database
 * @returns Object with counts for each repair status
 */
export async function getRepairStatusCounts() {
  const { data, error } = await supabase
    .from('repair_jobs')
    .select('status');

  if (error) {
    console.error("Error fetching repair status counts:", error);
    throw error;
  }

  const statusCounts = {
    pending: 0,
    inProgress: 0,
    completed: 0,
    delivered: 0,
    cancelled: 0,
    on_hold: 0
  };

  data.forEach(job => {
    if (statusCounts.hasOwnProperty(job.status)) {
      statusCounts[job.status]++;
    }
  });

  return statusCounts;
}

/**
 * Get revenue summary from the database
 * @returns Object with revenue statistics
 */
export async function getRevenueSummary() {
  // Get today's date at midnight
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Get first day of current week (Sunday)
  const firstDayOfWeek = new Date(today);
  firstDayOfWeek.setDate(today.getDate() - today.getDay());

  // Get first day of current month
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  // Fetch all invoices
  const { data, error } = await supabase
    .from('invoices')
    .select('created_at, total, paid_amount');

  if (error) {
    console.error("Error fetching revenue summary:", error);
    throw error;
  }

  // Calculate revenue statistics
  let todayRevenue = 0;
  let weekRevenue = 0;
  let monthRevenue = 0;
  let pendingAmount = 0;

  data.forEach(invoice => {
    const createdAt = new Date(invoice.created_at);
    const total = Number(invoice.total);
    const paidAmount = Number(invoice.paid_amount);

    // Add to pending amount
    pendingAmount += (total - paidAmount);

    // Check if invoice is from today
    if (createdAt >= today) {
      todayRevenue += paidAmount;
    }

    // Check if invoice is from this week
    if (createdAt >= firstDayOfWeek) {
      weekRevenue += paidAmount;
    }

    // Check if invoice is from this month
    if (createdAt >= firstDayOfMonth) {
      monthRevenue += paidAmount;
    }
  });

  return {
    today: todayRevenue,
    thisWeek: weekRevenue,
    thisMonth: monthRevenue,
    pending: pendingAmount
  };
}

/**
 * Get inventory items that are below their threshold
 * @returns Array of inventory items with low stock
 */
export async function getInventoryAlerts() {
  // First, get all inventory items
  const { data, error } = await supabase
    .from('inventory_items')
    .select('*');

  if (error) {
    console.error("Error fetching inventory alerts:", error);
    throw error;
  }

  // Filter items where quantity is less than or equal to threshold
  const lowStockItems = (data || []).filter(item => item.quantity <= item.threshold);

  return lowStockItems;
}

/**
 * Get recent repair jobs
 * @param count Number of repairs to return (default: 5)
 * @returns Array of recent repair jobs
 */
export async function getRecentRepairs(count = 5) {
  const { data, error } = await supabase
    .from('repair_jobs')
    .select('id, customer_id, status, device_type, brand, model, issue_description, updated_at, customers(name)')
    .order('updated_at', { ascending: false })
    .limit(count);

  if (error) {
    console.error("Error fetching recent repairs:", error);
    throw error;
  }

  // Transform data to match the expected format
  return (data || []).map(repair => ({
    id: repair.id,
    customerId: repair.customer_id,
    customerName: repair.customers?.name || 'Unknown Customer',
    deviceType: repair.device_type,
    brand: repair.brand,
    model: repair.model,
    issueDescription: repair.issue_description,
    status: repair.status,
    updatedAt: new Date(repair.updated_at)
  }));
}

/**
 * Get dashboard summary data
 * @returns Object with all dashboard data
 */
export async function getDashboardData() {
  try {
    const [statusCounts, revenueSummary, inventoryAlerts, recentRepairs] = await Promise.all([
      getRepairStatusCounts(),
      getRevenueSummary(),
      getInventoryAlerts(),
      getRecentRepairs()
    ]);

    return {
      statusCounts,
      revenueSummary,
      inventoryAlerts,
      recentRepairs
    };
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    throw error;
  }
}
