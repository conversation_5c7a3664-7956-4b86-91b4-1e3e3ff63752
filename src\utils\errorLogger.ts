/**
 * Error logging utility for the application
 * Captures and logs errors to the console and to Vercel
 */

// Define error severity levels
export enum ErrorSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

// Interface for error context
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  additionalData?: Record<string, any>;
}

// Flag to prevent recursive error logging
let isLoggingError = false;

// Counter to prevent excessive error logging
let errorCount = 0;
const MAX_ERRORS_PER_SESSION = 50;

/**
 * Log an error to the console and to Vercel
 * @param error The error object
 * @param severity The severity level of the error
 * @param context Additional context about where/when the error occurred
 */
export function logError(
  error: Error | string,
  severity: ErrorSeverity = ErrorSeverity.ERROR,
  context: ErrorContext = {}
): void {
  // Prevent recursive error logging
  if (isLoggingError) {
    return;
  }

  // Limit the number of errors logged per session
  if (errorCount >= MAX_ERRORS_PER_SESSION) {
    if (errorCount === MAX_ERRORS_PER_SESSION) {
      console.warn('[ErrorLogger] Maximum error count reached. Further errors will be suppressed.');
      errorCount++;
    }
    return;
  }

  errorCount++;
  isLoggingError = true;

  try {
    // Create a simplified error object to avoid circular references
    const errorObject = {
      message: error instanceof Error ? error.message : String(error).substring(0, 500),
      // Limit stack trace length
      stack: error instanceof Error ?
        (error.stack ? error.stack.split('\n').slice(0, 10).join('\n') : undefined) :
        undefined,
      severity,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      // Only include essential context to reduce payload size
      component: context.component,
      action: context.action,
    };

    // Log to console based on severity (with minimal output)
    const logMessage = `[ErrorLogger] ${errorObject.message}`;
    switch (severity) {
      case ErrorSeverity.INFO:
        console.info(logMessage);
        break;
      case ErrorSeverity.WARNING:
        console.warn(logMessage);
        break;
      case ErrorSeverity.ERROR:
      case ErrorSeverity.CRITICAL:
        console.error(logMessage);
        break;
      default:
        console.log(logMessage);
    }

    // Send to both Vercel Analytics (if available) and our custom logger
    if (typeof window !== 'undefined') {
      // First try Vercel Analytics
      try {
        // Prioritize using window.va since that's what's available
        if ('va' in window && window.va) {
          window.va('error', {
            name: errorObject.message.substring(0, 100),
            message: errorObject.message.substring(0, 100),
            severity: errorObject.severity,
            component: errorObject.component,
            action: errorObject.action,
            timestamp: new Date().toISOString()
          });
          console.info('[ErrorLogger] Sent error to Vercel Analytics using va function');

          // Also send as a custom event for better visibility
          window.va('event', {
            name: 'error_logged',
            severity: errorObject.severity,
            component: errorObject.component,
            action: errorObject.action,
            errorType: error instanceof Error ? error.constructor.name : 'Unknown',
            timestamp: new Date().toISOString()
          });
        }
        // Fallback to vercelAnalytics if available (less likely)
        else if ('vercelAnalytics' in window && window.vercelAnalytics) {
          window.vercelAnalytics.track('error', {
            name: errorObject.message.substring(0, 100),
            message: errorObject.message.substring(0, 100),
            severity: errorObject.severity,
            component: errorObject.component,
            action: errorObject.action,
          });
          console.info('[ErrorLogger] Sent error to Vercel Analytics using vercelAnalytics.track');
        }
      } catch (loggingError) {
        // Log if analytics tracking fails
        console.warn('[ErrorLogger] Failed to send to Vercel Analytics:', loggingError);
      }

      // Then always use our custom logger (works on free plan)
      try {
        if ('customLogger' in window) {
          // Use the appropriate severity level
          switch (severity) {
            case ErrorSeverity.INFO:
              window.customLogger.info(errorObject.message, {
                component: errorObject.component,
                action: errorObject.action,
                severity: errorObject.severity,
                stack: error instanceof Error ? error.stack : undefined
              });
              break;
            case ErrorSeverity.WARNING:
              window.customLogger.warn(errorObject.message, {
                component: errorObject.component,
                action: errorObject.action,
                severity: errorObject.severity,
                stack: error instanceof Error ? error.stack : undefined
              });
              break;
            case ErrorSeverity.ERROR:
            case ErrorSeverity.CRITICAL:
              window.customLogger.error(errorObject.message, {
                component: errorObject.component,
                action: errorObject.action,
                severity: errorObject.severity,
                stack: error instanceof Error ? error.stack : undefined,
                name: error instanceof Error ? error.name : 'Unknown'
              });
              break;
            default:
              window.customLogger.info(errorObject.message, {
                component: errorObject.component,
                action: errorObject.action,
                severity: errorObject.severity
              });
          }
          console.info('[ErrorLogger] Sent error to custom logger');
        }
      } catch (customLoggerError) {
        console.warn('[ErrorLogger] Failed to send to custom logger:', customLoggerError);
      }
    }
  } catch (e) {
    // Last resort fallback - if anything fails in our error logging, just log a simple message
    try {
      console.error('[ErrorLogger] Failed to log error');
    } catch (_) {
      // Do nothing if even this fails
    }
  } finally {
    isLoggingError = false;
  }
}

/**
 * Set up global error handlers to catch unhandled errors
 */
export function setupGlobalErrorHandlers(): void {
  if (typeof window !== 'undefined') {
    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      logError(
        event.reason || 'Unhandled Promise Rejection',
        ErrorSeverity.ERROR,
        { action: 'unhandledRejection' }
      );
    });

    // Capture global errors
    window.addEventListener('error', (event) => {
      logError(
        event.error || event.message,
        ErrorSeverity.ERROR,
        {
          action: 'globalError',
          additionalData: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
          },
        }
      );

      // Don't prevent the default error handling
      return false;
    });

    // Log when the page is about to be unloaded with errors
    window.addEventListener('beforeunload', () => {
      if (console.error.toString().indexOf('native') === -1) {
        logError(
          'Page unloaded with console errors',
          ErrorSeverity.WARNING,
          { action: 'pageUnload' }
        );
      }
    });
  }
}

// Flag to track if we're in a console capturing operation
let isCapturingConsole = false;

/**
 * Create a wrapped version of console methods to capture all console logs
 * with safeguards against recursion and excessive logging
 */
export function setupConsoleCapturing(): void {
  if (typeof window !== 'undefined') {
    // Store original console methods
    const originalConsole = {
      error: console.error,
      warn: console.warn,
      info: console.info,
      log: console.log,
    };

    // Override console.error
    console.error = function(...args) {
      // Prevent recursive logging
      if (isCapturingConsole || args[0] === '[ErrorLogger]') {
        // Call the original method without additional logging
        return originalConsole.error.apply(console, args);
      }

      isCapturingConsole = true;
      try {
        // Call the original method
        originalConsole.error.apply(console, args);

        // Only log non-ErrorLogger messages to prevent recursion
        const firstArg = args[0];
        if (typeof firstArg === 'string' && !firstArg.includes('[ErrorLogger]')) {
          // Create a simplified error message
          let errorMessage = '';
          try {
            // Only process the first argument to reduce complexity
            if (typeof firstArg === 'object') {
              errorMessage = firstArg instanceof Error ?
                firstArg.message :
                JSON.stringify(firstArg).substring(0, 200);
            } else {
              errorMessage = String(firstArg).substring(0, 200);
            }

            // Log with minimal context
            logError(errorMessage, ErrorSeverity.ERROR, { action: 'consoleError' });
          } catch (e) {
            // If stringifying fails, just use a generic message
            originalConsole.error('[ErrorLogger] Failed to process console error');
          }
        }
      } finally {
        isCapturingConsole = false;
      }
    };

    // Override console.warn with similar safeguards
    console.warn = function(...args) {
      // Prevent recursive logging
      if (isCapturingConsole || args[0] === '[ErrorLogger]') {
        // Call the original method without additional logging
        return originalConsole.warn.apply(console, args);
      }

      isCapturingConsole = true;
      try {
        // Call the original method
        originalConsole.warn.apply(console, args);

        // Only log non-ErrorLogger messages
        const firstArg = args[0];
        if (typeof firstArg === 'string' && !firstArg.includes('[ErrorLogger]')) {
          // Create a simplified warning message
          let warnMessage = '';
          try {
            // Only process the first argument
            if (typeof firstArg === 'object') {
              warnMessage = JSON.stringify(firstArg).substring(0, 200);
            } else {
              warnMessage = String(firstArg).substring(0, 200);
            }

            // Log with minimal context
            logError(warnMessage, ErrorSeverity.WARNING, { action: 'consoleWarn' });
          } catch (e) {
            // If stringifying fails, just use a generic message
            originalConsole.warn('[ErrorLogger] Failed to process console warning');
          }
        }
      } finally {
        isCapturingConsole = false;
      }
    };
  }
}
