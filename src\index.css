
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;

    --sidebar-background: 223 100% 98%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 14.3% 95.9%;
    --sidebar-accent-foreground: 220.9 39.3% 11%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 224.3 76.3% 48%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --sidebar-background: 222.2 47.4% 11.2%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:-translate-y-1;
  }

  .stats-card {
    @apply bg-white rounded-lg shadow p-4 flex flex-col card-hover;
  }

  .stats-value {
    @apply text-3xl font-bold mt-2;
  }

  .stats-label {
    @apply text-sm text-gray-500;
  }
}

/* Print styles */
@media print {
  /* Hide elements not needed for printing */
  .print\:hidden,
  .print-hidden,
  nav,
  header,
  footer,
  button,
  .sidebar,
  .dialog-overlay {
    display: none !important;
  }

  /* Show elements that should only appear when printing */
  .print\:block {
    display: block !important;
  }

  /* Format the invoice for printing */
  .print-content {
    width: 100% !important;
    margin: 0 !important;
    padding: 20px !important;
    background-color: white !important;
    color: black !important;
    font-size: 12pt !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
  }

  /* Format cards for printing */
  .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
    break-inside: avoid !important;
    margin-bottom: 20px !important;
  }

  /* Ensure tables print properly */
  table {
    width: 100% !important;
    border-collapse: collapse !important;
  }

  th, td {
    border-bottom: 1px solid #ddd !important;
    padding: 8px !important;
    text-align: left !important;
  }

  /* Ensure page breaks don't occur in the middle of important content */
  .card, table, tr {
    page-break-inside: avoid !important;
  }

  /* Add page margins */
  @page {
    margin: 1.5cm !important;
    size: auto !important;
  }

  /* Reset body styles for printing */
  body {
    min-height: auto !important;
    background-color: white !important;
    margin: 0 !important;
    padding: 0 !important;
    height: auto !important;
  }

  /* Ensure only one copy of the invoice is printed */
  body > *:not(.print-content) {
    display: none !important;
  }
}
