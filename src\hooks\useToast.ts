import { useCallback } from 'react';
import { toast, ToastOptions } from '@/components/ui/sonner';

interface ToastConfig extends ToastOptions {
  description?: string;
}

/**
 * A hook for simplified toast notifications
 * 
 * @returns Methods for showing different types of toast notifications
 */
export function useToast() {
  // Success toast
  const success = useCallback((message: string, config?: ToastConfig) => {
    toast.success(message, config);
  }, []);

  // Error toast
  const error = useCallback((message: string, config?: ToastConfig) => {
    toast.error(message, config);
  }, []);

  // Info toast
  const info = useCallback((message: string, config?: ToastConfig) => {
    toast.info(message, config);
  }, []);

  // Warning toast
  const warning = useCallback((message: string, config?: ToastConfig) => {
    toast.warning(message, config);
  }, []);

  // Custom toast
  const custom = useCallback((message: string, config?: ToastConfig) => {
    toast(message, config);
  }, []);

  // Promise toast
  const promise = useCallback(<T>(
    promise: Promise<T>,
    {
      loading = 'Loading...',
      success = 'Success!',
      error = 'Error!'
    }: {
      loading?: string;
      success?: string | ((data: T) => string);
      error?: string | ((error: Error) => string);
    },
    options?: ToastOptions
  ) => {
    return toast.promise(promise, {
      loading,
      success,
      error
    }, options);
  }, []);

  // API request toast
  const apiRequest = useCallback(async <T>(
    requestFn: () => Promise<T>,
    {
      loading = 'Processing...',
      success = 'Operation completed successfully',
      error = 'An error occurred'
    }: {
      loading?: string;
      success?: string | ((data: T) => string);
      error?: string | ((error: Error) => string);
    },
    options?: ToastOptions
  ): Promise<T | undefined> => {
    try {
      toast.loading(loading, options);
      const result = await requestFn();
      
      toast.dismiss();
      
      const successMessage = typeof success === 'function'
        ? success(result)
        : success;
      
      toast.success(successMessage, options);
      
      return result;
    } catch (err) {
      toast.dismiss();
      
      const errorMessage = typeof error === 'function' && err instanceof Error
        ? error(err)
        : error;
      
      toast.error(errorMessage, {
        ...options,
        description: err instanceof Error ? err.message : undefined
      });
      
      return undefined;
    }
  }, []);

  return {
    success,
    error,
    info,
    warning,
    custom,
    promise,
    apiRequest,
    dismiss: toast.dismiss
  };
}
