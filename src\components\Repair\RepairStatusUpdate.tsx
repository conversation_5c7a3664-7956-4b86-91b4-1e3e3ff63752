import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RepairJob, RepairStatus } from "@/lib/data";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Printer } from "lucide-react";
import { useAppContext } from "@/contexts/AppContext";
import { toast } from "sonner";
import InventoryItemSelector from "./InventoryItemSelector";
import RepairTicketPrint from "./RepairTicketPrint";

interface RepairStatusUpdateProps {
  repair: RepairJob;
  onSuccess: () => void;
  onCancel: () => void;
}

const RepairStatusUpdate: React.FC<RepairStatusUpdateProps> = ({
  repair,
  onSuccess,
  onCancel
}) => {
  const { updateRepairJob, updateInventoryItem, inventory } = useAppContext();
  const [status, setStatus] = useState<RepairStatus>(repair.status);
  const [notes, setNotes] = useState(Array.isArray(repair.notes) ? repair.notes.join('\n') : (repair.notes || ""));
  const [finalCost, setFinalCost] = useState(repair.finalCost?.toString() || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDelivered, setIsDelivered] = useState(status === 'delivered');
  const [selectedParts, setSelectedParts] = useState<Array<{ id: string; name: string; quantity: number; cost: number }>>(
    repair.partsUsed || []
  );

  // Update isDelivered when status changes
  useEffect(() => {
    setIsDelivered(status === 'delivered');
  }, [status]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Calculate total cost of parts
      const partsCost = selectedParts.reduce((sum, part) => sum + (part.cost * part.quantity), 0);

      // If final cost is not set, use the parts cost as a base
      const updatedFinalCost = finalCost ? parseFloat(finalCost) : partsCost;

      // Convert notes string to array for PostgreSQL compatibility
      const notesArray = notes.trim() ? notes.split('\n').filter(note => note.trim()) : [];

      // If changing to delivered status, add a note about parts being finalized
      if (status === 'delivered' && repair.status !== 'delivered') {
        notesArray.push(`Parts finalized on ${new Date().toLocaleDateString()}: ${selectedParts.map(p => `${p.name} (${p.quantity})`).join(', ')}`);
      }

      // Update repair job with new status, notes, and parts used
      const updatedRepair = await updateRepairJob({
        ...repair,
        status,
        notes: notesArray,
        finalCost: updatedFinalCost,
        partsUsed: selectedParts,
        completedAt: status === 'completed' ? new Date() : repair.completedAt,
        deliveredAt: status === 'delivered' ? new Date() : repair.deliveredAt
      });

      // Update inventory quantities for the parts used
      if (selectedParts.length > 0) {
        // Get the difference between the original parts and the new parts
        const originalParts = repair.partsUsed || [];

        // For each selected part, update inventory
        for (const part of selectedParts) {
          // Find the original quantity used (if any)
          const originalPart = originalParts.find(p => p.id === part.id);
          const originalQuantity = originalPart ? originalPart.quantity : 0;

          // Only update inventory if quantity has changed
          if (part.quantity !== originalQuantity) {
            try {
              // Get current inventory item from context
              const inventoryItem = inventory.find(item => item.id === part.id);

              if (inventoryItem) {
                // Calculate the difference in quantity
                const quantityDifference = part.quantity - originalQuantity;

                // Update inventory quantity
                await updateInventoryItem({
                  ...inventoryItem,
                  quantity: Math.max(0, inventoryItem.quantity - quantityDifference)
                });
              }
            } catch (error) {
              console.error(`Error updating inventory for part ${part.id}:`, error);
              // Don't fail the whole operation if inventory update fails
              toast.error(`Failed to update inventory for ${part.name}`);
            }
          }
        }
      }

      onSuccess();
    } catch (error) {
      console.error("Error updating repair status:", error);
      toast.error("Failed to update repair status");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="status">Repair Status</Label>
          <Select
            value={status}
            onValueChange={(value) => setStatus(value as RepairStatus)}
          >
            <SelectTrigger id="status">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="on_hold">On Hold</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="finalCost">Final Cost</Label>
          <Input
            id="finalCost"
            type="number"
            min="0"
            step="1"
            value={finalCost}
            onChange={(e) => setFinalCost(e.target.value)}
            placeholder="Enter final cost"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Leave empty to calculate based on parts cost
          </p>
        </div>

        <div>
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add repair notes..."
            rows={4}
          />
        </div>

        <div>
          {isDelivered && (
            <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-yellow-800 text-sm">
                <strong>Note:</strong> Parts cannot be modified for delivered repairs. The parts list is locked when a repair is marked as delivered.
              </p>
            </div>
          )}

          <InventoryItemSelector
            onItemsSelected={setSelectedParts}
            initialItems={repair.partsUsed}
            disabled={isDelivered}
          />
        </div>
      </div>

      <div className="flex justify-between gap-2">
        <div>
          {/* Print Ticket Button */}
          <RepairTicketPrint
            repair={repair}
            buttonText="Print Ticket"
            buttonVariant="outline"
            buttonSize="default"
            onPrintSuccess={() => {}}
            onPrintError={(error) => {
              console.error("Print error:", error);
            }}
          />
        </div>
        <div className="flex gap-2">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isSubmitting ? "Updating..." : "Update Repair"}
          </Button>
        </div>
      </div>
    </form>
  );
};

export default RepairStatusUpdate;
