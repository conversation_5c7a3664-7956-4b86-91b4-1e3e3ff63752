import { test, expect } from '@playwright/test';

test.setTimeout(120000); // Increase timeout to 2 minutes

test('Debug invoice creation', async ({ page }) => {
  // Navigate to the repairs page
  await page.goto('http://localhost:8080/repairs', { timeout: 60000 });

  // Wait for the page to load
  await page.waitForSelector('h1:has-text("Repair Jobs")');

  // Find a completed repair and click the Invoice button
  const completedRepairRow = page.locator('tr:has-text("completed")').first();

  if (await completedRepairRow.count() === 0) {
    console.log('No completed repairs found. Creating a new repair and marking it as completed...');

    // Click the "New Repair" button
    await page.click('button:has-text("New Repair")');

    // Fill out the repair form
    await page.fill('input[name="customerName"]', 'Test Customer');
    await page.fill('input[name="deviceType"]', 'Smartphone');
    await page.fill('input[name="brand"]', 'Test Brand');
    await page.fill('input[name="model"]', 'Test Model');
    await page.fill('textarea[name="issueDescription"]', 'Test issue for invoice debugging');
    await page.fill('input[name="estimatedCost"]', '1000');

    // Submit the form
    await page.click('button:has-text("Create Repair Job")');

    // Wait for the repair to be created
    await page.waitForSelector('text=Repair job created successfully');

    // Find the newly created repair and update its status to completed
    const newRepairRow = page.locator('tr:has-text("Test Brand")').first();

    await newRepairRow.locator('button[aria-label="Edit Status"]').click();

    // Click the "Completed" button
    await page.click('button:has-text("Completed")');

    // Wait for the status update
    await page.waitForSelector('text=Status updated to Completed');

    // Refresh the page to ensure we see the updated status
    await page.reload();

    // Find the completed repair again
    await page.waitForSelector('tr:has-text("completed")');
  }

  // Now find a completed repair and click the Invoice button
  const invoiceButton = page.locator('tr:has-text("completed")').first().locator('button:has-text("Invoice")');

  console.log('Clicking the Invoice button...');
  await invoiceButton.click();

  // Wait for the invoice form to appear
  await page.waitForSelector('h2:has-text("Generate Invoice")');

  // Log the repair job data
  const repairJobData = await page.evaluate(() => {
    // @ts-ignore
    return window.__DEBUG_REPAIR_JOB__;
  });
  console.log('Repair job data:', repairJobData);

  // Fill out the invoice form
  console.log('Filling out the invoice form...');

  // Make sure we have at least one item
  const itemsCount = await page.locator('input[id^="items."][id$=".description"]').count();
  if (itemsCount === 0) {
    await page.click('button:has-text("Add Item")');
  }

  // Fill out the first item
  await page.fill('input[id^="items.0.description"]', 'Test Item');
  await page.fill('input[id^="items.0.quantity"]', '1');
  await page.fill('input[id^="items.0.unitPrice"]', '1000');

  // Set tax rate
  await page.fill('input[id="taxRate"]', '10');

  // Set paid amount
  await page.fill('input[id="paidAmount"]', '1100');

  // Add notes
  await page.fill('textarea[id="notes"]', 'Test invoice for debugging');

  // Submit the form
  console.log('Submitting the invoice form...');
  await page.click('button:has-text("Create Invoice")');

  // Wait for response
  try {
    await page.waitForSelector('text=Invoice created successfully', { timeout: 5000 });
    console.log('Invoice created successfully!');
  } catch (error) {
    console.log('Invoice creation failed. Checking for error messages...');

    // Check for error toast
    const errorToast = await page.locator('div[role="status"]:has-text("Failed")').count();
    if (errorToast > 0) {
      const errorText = await page.locator('div[role="status"]:has-text("Failed")').textContent();
      console.log('Error toast found:', errorText);
    }

    // Check console logs
    const consoleLogs = await page.evaluate(() => {
      // @ts-ignore
      return window.__CONSOLE_LOGS__ || [];
    });
    console.log('Console logs:', consoleLogs);

    throw new Error('Invoice creation failed');
  }

  // Check if the invoice preview is shown
  const invoicePreview = await page.locator('h2:has-text("Invoice Preview")').count();
  if (invoicePreview > 0) {
    console.log('Invoice preview is shown. Invoice was created successfully.');
  } else {
    console.log('Invoice preview is not shown. Something went wrong.');
  }
});
