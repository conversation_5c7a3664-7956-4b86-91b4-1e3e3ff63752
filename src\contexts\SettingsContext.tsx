import React, { createContext, useContext, useState, useEffect } from "react";
import { getSettings, saveSettings, Settings, DEFAULT_SETTINGS } from "@/services/settings";
import { toast } from "sonner";

interface SettingsContextType {
  settings: Settings;
  isLoading: boolean;
  saveSettings: (settings: Settings) => Promise<void>;
  resetSettings: () => Promise<void>;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<Settings>({} as Settings);
  const [isLoading, setIsLoading] = useState(true);

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true);
        const loadedSettings = await getSettings();
        setSettings(loadedSettings);
      } catch (error) {
        console.error("Error loading settings:", error);
        toast.error("Failed to load settings");
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Save settings
  const handleSaveSettings = async (newSettings: Settings) => {
    try {
      setIsLoading(true);
      const updatedSettings = await saveSettings(newSettings);
      setSettings(updatedSettings);
      toast.success("Settings saved successfully");
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    } finally {
      setIsLoading(false);
    }
  };

  // Reset settings to defaults
  const resetSettings = async () => {
    try {
      setIsLoading(true);
      // Use the default settings and save them to the database
      const updatedSettings = await saveSettings(DEFAULT_SETTINGS);
      setSettings(updatedSettings);
      toast.success("Settings reset to defaults");
    } catch (error) {
      console.error("Error resetting settings:", error);
      toast.error("Failed to reset settings");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SettingsContext.Provider
      value={{
        settings,
        isLoading,
        saveSettings: handleSaveSettings,
        resetSettings,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
};


