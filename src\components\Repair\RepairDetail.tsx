import React from "react";
import { RepairJob } from "@/lib/data";
import { formatINR } from "@/lib/currency";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  CalendarClock,
  Phone,
  User,
  Wrench,
  Smartphone,
  ClipboardList,
  FileText,
  CheckCircle,
  Truck,
  Edit,
  PencilLine,
  Printer
} from "lucide-react";
import RepairStatusBadge from "./RepairStatusBadge";
import RepairTicketPrint from "./RepairTicketPrint";

interface RepairDetailProps {
  repair: RepairJob;
  onClose: () => void;
  onGenerateInvoice?: (repairId: string) => void;
  onUpdateStatus?: (repair: RepairJob) => void;
  onEdit?: (repair: RepairJob) => void;
}

const RepairDetail: React.FC<RepairDetailProps> = ({
  repair,
  onClose,
  onGenerateInvoice,
  onUpdateStatus,
  onEdit
}) => {
  // Format dates
  const formatDate = (date: Date | undefined) => {
    if (!date) return "Not set";
    return date.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header with basic info */}
      <div>
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-semibold">
              {repair.brand} {repair.model}
            </h2>
            <p className="text-sm text-muted-foreground">
              ID: {repair.id}
            </p>
          </div>
          <RepairStatusBadge status={repair.status} />
        </div>

        <div className="mt-2 flex flex-wrap gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <Smartphone className="h-3 w-3" />
            {repair.deviceType}
          </Badge>
          {repair.serialNumber && (
            <Badge variant="outline" className="flex items-center gap-1">
              <ClipboardList className="h-3 w-3" />
              S/N: {repair.serialNumber}
            </Badge>
          )}
        </div>
      </div>

      <Separator />

      {/* Customer info */}
      <div>
        <h3 className="text-sm font-medium mb-2">Customer Information</h3>
        <div className="grid grid-cols-1 gap-2">
          <div className="flex items-center">
            <User className="h-4 w-4 mr-2 text-muted-foreground" />
            <span>{repair.customerName}</span>
          </div>
        </div>
      </div>

      <Separator />

      {/* Issue description */}
      <div>
        <h3 className="text-sm font-medium mb-2">Issue Description</h3>
        <div className="bg-muted/50 p-3 rounded-md text-sm">
          {repair.issueDescription}
        </div>
      </div>

      {/* Repair details */}
      <div>
        <h3 className="text-sm font-medium mb-2">Repair Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center">
              <Wrench className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-sm">Status: <span className="font-medium">{repair.status.charAt(0).toUpperCase() + repair.status.slice(1)}</span></span>
            </div>
            {repair.technicianName && (
              <div className="flex items-center">
                <Wrench className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">Technician: <span className="font-medium">{repair.technicianName}</span></span>
              </div>
            )}
          </div>
          <div className="space-y-2">
            <div className="flex items-center">
              <CalendarClock className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="text-sm">Created: <span className="font-medium">{formatDate(repair.createdAt)}</span></span>
            </div>
            {repair.completedAt && (
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">Completed: <span className="font-medium">{formatDate(repair.completedAt)}</span></span>
              </div>
            )}
            {repair.deliveredAt && (
              <div className="flex items-center">
                <Truck className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">Delivered: <span className="font-medium">{formatDate(repair.deliveredAt)}</span></span>
              </div>
            )}
          </div>
        </div>
      </div>

      <Separator />

      {/* Cost information */}
      <div>
        <h3 className="text-sm font-medium mb-2">Cost Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm">Estimated Cost:</p>
            <p className="text-lg font-semibold">
              {repair.estimatedCost ? formatINR(repair.estimatedCost) : "Not estimated"}
            </p>
          </div>
          <div>
            <p className="text-sm">Final Cost:</p>
            <p className="text-lg font-semibold">
              {repair.finalCost ? formatINR(repair.finalCost) : "Not finalized"}
            </p>
          </div>
        </div>
      </div>

      {/* Parts used */}
      {repair.partsUsed && repair.partsUsed.length > 0 && (
        <>
          <Separator />
          <div>
            <h3 className="text-sm font-medium mb-2">Parts Used</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="py-2 px-3 text-left">Part</th>
                    <th className="py-2 px-3 text-center">Quantity</th>
                    <th className="py-2 px-3 text-right">Cost</th>
                  </tr>
                </thead>
                <tbody>
                  {repair.partsUsed.map((part, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-2 px-3">{part.name}</td>
                      <td className="py-2 px-3 text-center">{part.quantity}</td>
                      <td className="py-2 px-3 text-right">{formatINR(part.cost)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}

      {/* Notes */}
      {repair.notes && (
        <>
          <Separator />
          <div>
            <h3 className="text-sm font-medium mb-2">Notes</h3>
            <div className="bg-muted/50 p-3 rounded-md text-sm">
              {repair.notes}
            </div>
          </div>
        </>
      )}

      {/* Actions */}
      <div className="flex flex-wrap justify-end gap-2 pt-4">
        {/* Print Ticket Button */}
        <RepairTicketPrint
          repair={repair}
          buttonText="Print Ticket"
          buttonVariant="outline"
          buttonSize="default"
          onPrintSuccess={() => {
            console.log("Print success callback triggered");
          }}
          onPrintError={(error) => {
            console.error("Print error:", error);
          }}
        />

        {repair.status === 'completed' && onGenerateInvoice && (
          <Button
            variant="outline"
            onClick={() => onGenerateInvoice(repair.id)}
            className="flex items-center gap-1"
          >
            <FileText className="h-4 w-4" />
            Generate Invoice
          </Button>
        )}
        {onUpdateStatus && (
          <Button
            variant="outline"
            onClick={() => onUpdateStatus(repair)}
          >
            Update Status
          </Button>
        )}
        {onEdit && (
          <Button
            variant="outline"
            onClick={() => onEdit(repair)}
            className="flex items-center gap-1"
          >
            <PencilLine className="h-4 w-4" />
            Edit Repair
          </Button>
        )}
        <Button onClick={onClose}>Close</Button>
      </div>
    </div>
  );
};

export default RepairDetail;
