// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jjbmfyppkfowgjywpryb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpqYm1meXBwa2Zvd2dqeXdwcnliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ4MDM5NjYsImV4cCI6MjA2MDM3OTk2Nn0.u_bJ3pz5c1emdOXlvdabD1OntjaDRlguJh7fpQPOBbI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);