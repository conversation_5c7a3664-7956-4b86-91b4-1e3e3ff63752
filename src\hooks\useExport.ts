import { useState } from 'react';

type ExportFormat = 'csv' | 'json' | 'pdf';

interface ExportOptions {
  fileName?: string;
  includeHeaders?: boolean;
  delimiter?: string;
  dateFormat?: (date: Date) => string;
  customFormatters?: Record<string, (value: any) => string>;
}

/**
 * A hook for exporting data in various formats
 * 
 * @returns Methods for exporting data and loading state
 */
export function useExport() {
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Format a date according to the provided format or default to ISO string
   */
  const formatDate = (date: Date, format?: (date: Date) => string): string => {
    if (!date) return '';
    if (format) return format(date);
    
    // Default format: DD/MM/YYYY
    return `${date.getDate().toString().padStart(2, '0')}/${
      (date.getMonth() + 1).toString().padStart(2, '0')}/${
      date.getFullYear()}`;
  };

  /**
   * Convert a value to a string for CSV export
   */
  const formatValueForCsv = (
    value: any, 
    key: string, 
    options?: ExportOptions
  ): string => {
    if (value === null || value === undefined) return '';
    
    // Use custom formatter if provided
    if (options?.customFormatters && options.customFormatters[key]) {
      return options.customFormatters[key](value);
    }
    
    // Handle dates
    if (value instanceof Date) {
      return formatDate(value, options?.dateFormat);
    }
    
    // Handle arrays
    if (Array.isArray(value)) {
      return `"${value.join(', ')}"`;
    }
    
    // Handle objects
    if (typeof value === 'object') {
      return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
    }
    
    // Handle strings that need escaping
    if (typeof value === 'string') {
      // Escape quotes and wrap in quotes if contains delimiter, newline, or quotes
      if (
        value.includes(options?.delimiter || ',') || 
        value.includes('\n') || 
        value.includes('"')
      ) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }
    
    // Return as string for other types
    return String(value);
  };

  /**
   * Export data as CSV
   */
  const exportToCsv = async <T extends Record<string, any>>(
    data: T[],
    options: ExportOptions = {}
  ): Promise<void> => {
    try {
      setIsExporting(true);
      setError(null);
      
      if (!data || !data.length) {
        throw new Error('No data to export');
      }
      
      const {
        fileName = 'export.csv',
        includeHeaders = true,
        delimiter = ',',
      } = options;
      
      // Get all unique keys from the data
      const allKeys = Array.from(
        new Set(
          data.flatMap(item => Object.keys(item))
        )
      );
      
      // Create CSV content
      let csvContent = '';
      
      // Add headers if requested
      if (includeHeaders) {
        csvContent += allKeys.map(key => formatValueForCsv(key, key, options)).join(delimiter) + '\n';
      }
      
      // Add data rows
      csvContent += data.map(item => {
        return allKeys.map(key => formatValueForCsv(item[key], key, options)).join(delimiter);
      }).join('\n');
      
      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setIsExporting(false);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
      setIsExporting(false);
      throw err;
    }
  };

  /**
   * Export data as JSON
   */
  const exportToJson = async <T extends Record<string, any>>(
    data: T[],
    options: ExportOptions = {}
  ): Promise<void> => {
    try {
      setIsExporting(true);
      setError(null);
      
      if (!data || !data.length) {
        throw new Error('No data to export');
      }
      
      const { fileName = 'export.json' } = options;
      
      // Create a blob and download link
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setIsExporting(false);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
      setIsExporting(false);
      throw err;
    }
  };

  /**
   * Export data in the specified format
   */
  const exportData = async <T extends Record<string, any>>(
    data: T[],
    format: ExportFormat = 'csv',
    options: ExportOptions = {}
  ): Promise<void> => {
    switch (format) {
      case 'csv':
        return exportToCsv(data, options);
      case 'json':
        return exportToJson(data, options);
      case 'pdf':
        throw new Error('PDF export not implemented yet');
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  };

  return {
    exportData,
    exportToCsv,
    exportToJson,
    isExporting,
    error
  };
}
