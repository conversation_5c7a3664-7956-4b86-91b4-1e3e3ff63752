<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="." />
    <title>gadget-repair-flow</title>
    <meta name="description" content="Lovable Generated Project" />
    <meta name="author" content="Lovable" />

    <meta property="og:title" content="gadget-repair-flow" />
    <meta property="og:description" content="Lovable Generated Project" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- Custom Logger for Vercel Free Plan -->
    <script src="/custom-logger.js"></script>

    <!-- Vercel Analytics (if available) -->
    <script>
      window.va = function(command, ...args) {
        (window.va.q = window.va.q || []).push([command, ...args]);
        console.log('Vercel Analytics:', command, ...args);
      };
      window.va.q = window.va.q || [];

      // Send an initialization event
      window.va('event', {
        name: 'page_initialized',
        url: window.location.href,
        timestamp: new Date().toISOString()
      });
    </script>
    <script defer src="/_vercel/insights/script.js"></script>
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="./src/main.tsx"></script>
  </body>
</html>
